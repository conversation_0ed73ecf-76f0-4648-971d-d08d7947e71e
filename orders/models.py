from django.db import models
from django.utils.translation import gettext_lazy as _ 
from django.core.validators import MinValueValidator

class OrderStatus(models.TextChoices):
    PENDING = 'pending', _('Pending')
    CONFIRMED = 'confirmed', _('Confirmed')
    PREPARING = 'preparing', _('Preparing')
    ON_DELIVERY = 'on_delivery', _('On Delivery')
    DELIVERED = 'delivered', _('Delivered')
    CANCELLED = 'cancelled', _('Cancelled')

class PaymentMethod(models.TextChoices):
    CASH = 'cash', _('Cash')
    CARD = 'card', _('Card')
    ONLINE = 'online', _('Online')

class Order(models.Model):
    customer = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='orders'
    )
    status = models.CharField(
        max_length=20,
        choices=OrderStatus.choices,
        default=OrderStatus.PENDING
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    payment_method = models.CharField(
        max_length=20,
        choices=PaymentMethod.choices,
        default=PaymentMethod.CASH
    )
    is_paid = models.BooleanField(default=False)
    delivery_address = models.TextField()
    delivery_instructions = models.TextField(blank=True)
    delivery_Person = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='deliveries'
    )
    customer_phone = models.CharField(max_length=20)

    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Order #{self.id} {self.get_status_display()}"

class OrderItem(models.Model):
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='order_items'
    )
    menu_item = models.ForeignKey(
        'menu.MenuItem',
        on_delete=models.PROTECT,
        related_name='order_items'
    )
    quantity = models.PositiveIntegerField(default=1)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    special_instructions = models.TextField(blank=True)

    @property
    def total_price(self):
        return self.quantity * self.price
    
    def __str__(self):
        return f"{self.quantity}x {self.menu_items.name} for  Order # {self.order.id}"

class PaymentTransaction(models.Model):
    order = models.OneToOneField(
        Order,
        on_delete=models.CASCADE,
        related_name='payment_transaction'
    )
    amount = models.DecimalField(decimal_places=2, max_digits=10)
    transaction_id = models.CharField(max_length=100)
    payment_method = models.CharField(max_length=50)
    status = models.CharField(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    processes_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Payment for Order #{self.order.id}"

