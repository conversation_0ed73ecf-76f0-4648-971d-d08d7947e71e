from django.contrib import admin
from .models import Order, OrderItem, PaymentTransaction

class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'customer', 'status', 'total_amount', 'created_at')
    list_filter = ('status', 'created_at', 'payment_method')
    search_fields = ('customer__username', 'id')
    inlines = [OrderItemInline]
    read_only_fields = ('created_at', 'updated_at')

@admin.register(PaymentTransaction)
class PaymentTransactionAdmin(admin.ModelAdmin):
    list_display = ('order', 'amount', 'status', 'created_at')
    list_filter = ('status', 'payment_method')
    search_fields = ('order__id', 'transaction_id')
    read_only_fields = ('created_at', 'processed_at')
