from rest_framework import viewsets, permissions, status
from rest_framework.response import Response 
from orders.models import Order, PaymentTransaction
from orders.api.serializers import OrderSerializer, PaymentTransactionSerializer
from django_tenants.utils import tenant_context
from django.db import transaction 

class OrderViewSet(viewsets.ModelViewSet):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['status', 'payment_method', 'is_paid']
    queryset = Order.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            if self.request.user.is_customer:
                return Order.objects.filter(customer=self.request.user)
            return Order.objects.all()
    
    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save(customer=self.request.user)

class PaymentTransactionViewSet(viewsets.ModelViewSet):
    serializer_class = PaymentTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = PaymentTransaction.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            return PaymentTransaction.objects.filter(order__customer=self.request.user)
