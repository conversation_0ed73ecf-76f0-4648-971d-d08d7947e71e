from rest_framework import serializers
from orders.models import Order, OrderItem, PaymentTransaction
from users.api.serializers import UserSerializer

class OrderItemSerializer(serializers.ModelSerializer):
    class Meta:
        models = OrderItem
        fields = ['id', 'menu_item', 'quantity', 'price', 'special_instructions']

class PaymentTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentTransaction
        fields = ['id', 'amount', 'transaction_id', 'payment_method', 'status', 'created_at']
    
class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True)
    customer = UserSerializer(read_only=True)
    delivery_person =UserSerializer(read_only=True)
    payment = PaymentTransactionSerializer(source='payment_transaction', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'customer', 'status', 'created_at', 'updated_at', 'total_amount', 'payment_method',
            'is_paid', 'delivery_address', 'delivery_instructions', 'delivery_person', 'items', 'payment'
        ]
        read_only_fields = ['status', 'created_at', 'updated_at', 'total_amount']
    
    def create(self, validated_data):
        items_data = validated_data.pop('items')
        order = Order.objects.create(**validated_data)

        for item_data in items_data:
            OrderItem.objects.create(order=order, **item_data)
        return order
