from django.contrib import admin
from .models import Category, MenuItem

class MenuItemInline(admin.TabularInline):
    model = MenuItem
    extra = 1

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active','order')
    list_editable = ('is_active', 'order')
    inlines = [MenuItemInline]

@admin.register(MenuItem)
class MenuItemAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'price', 'is_available')
    list_filter = ('category', 'is_available', 'is_vegetarian', 'is_vegan')
    search_fields = ('name', 'description')
    list_editable = ('price', 'is_available')
