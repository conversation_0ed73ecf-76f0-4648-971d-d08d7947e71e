from rest_framework import serializers
from menu.models import MenuItem, Category 

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'is_active', 'order']
        read_only_fields = ['restaurant']

class MenuItemSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(),
        source='category',
        write_only=True
    )

    class Meta:
        model = MenuItem
        fields = [
            'id',
            'name', 'description', 'price', 'image', 'preparation_time',
            'is_available', 'is_vegetarian', 'is_vegan', 'calories', 'is_glutten_free',
            'category',
            'category_id'
        ]
        extra_kwargs = {
            'image': {'required': False}
        }
        read_only_fields = ['restaurant']
    
    def __init__(self, *args, **kwargs):
           super().__init__(*args, **kwargs)
           # Restrict category choices to current tenant
           request = self.context.get('request')
           if request and hasattr(request, 'tenant'):
               self.fields['category_id'].queryset = Category.objects.filter(
                   restaurant=request.tenant
               )

"""
from rest_framework import serializers
from menu.models import MenuItem, Category 

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['category_id', 'name', 'description', 'is_active', 'order']

class MenuItemSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(),
        source=Category,
        write_only=True
    )

    class Meta:
        model = MenuItem
        fields = [
            'name', 'description', 'price', 'image', 'preparation_time',
            'is_available', 'is_vegetarian', 'is_vegan', 'calories', 'is_glutten_free'
        ]

        extra_kwargs = {
            'image': {'requied': False}
        }
    """
    
