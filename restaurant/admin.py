from django.contrib import admin
from .models import Payment, Notification, RestaurantConfiguration

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('tenant', 'amount', 'date', 'payment_method')
    list_filter = ('payment_method', 'date')
    search_fields = ('tenant__name', 'transaction_id')

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('tenant', 'recipient', 'title', 'created_at', 'is_read')
    list_filter = ('is_read', 'created_at')
    search_fields = ('tenant__name', 'recipient__name', 'delivery_radius_km')
