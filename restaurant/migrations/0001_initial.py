# Generated by Django 5.1.9 on 2025-06-05 10:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('date', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('payment_method', models.Char<PERSON>ield(max_length=50)),
                ('trasaction_id', models.Char<PERSON>ield(max_length=100)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['date'],
            },
        ),
        migrations.CreateModel(
            name='RestaurantConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enable_online_payments', models.BooleanField(default=True)),
                ('enable_cash_on_delivery', models.BooleanField(verbose_name=True)),
                ('delivery_radius_km', models.PositiveIntegerField(default=10)),
                ('minimum_order_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('delivery_fee', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('order_confirmation_sms', models.BooleanField(default=True)),
                ('order_confirmation_email', models.BooleanField(default=True)),
                ('opening_time', models.TimeField()),
                ('closing_time', models.TimeField()),
            ],
        ),
        migrations.CreateModel(
            name='RestaurantTheme',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('primary_color', models.CharField(default='#FF6600', max_length=7)),
                ('secondary_color', models.CharField(default='#FFFFFF', max_length=7)),
                ('accent_color', models.CharField(default='#FFD700', max_length=7)),
                ('font_family', models.CharField(default='Roboto', max_length=50)),
                ('rounded_corners', models.BooleanField(default=True)),
                ('dark_mode', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
