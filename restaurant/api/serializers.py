from rest_framework import serializers
from restaurant.models import Payment, Notification, RestaurantConfiguration
from rest_framework import serializers
from users.models import User, UserRole
import random
from tenants.models import Client

class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = ['id', 'amount', 'date', 'payment_method', 'transaction_id', 'description']

class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'title', 'message', 'created_at', 'is_read']
        read_only_fields = ['created_at', 'is_read']
    
class RestaurantConfigurationSerializer(serializers.ModelSerializer):
    class Meta:
        model = RestaurantConfiguration
        fields = [
            'enable_online_payment', 'enable_cash_on_delivery', 'delivery_radius_km',
            'minimum_order_amount', 'opening_time', 'closing_time', 'order_confirmation_email',
            'order_confirmation_sms', 'delivery_fee'
        ]

class RestaurantSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = [
            'restaurant_name', 'address', 'description', 
            'logo', 'banner', 'restaurant_phone'
        ]
