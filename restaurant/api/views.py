from rest_framework import viewsets,permissions
from restaurant.models import Payment, Notification, RestaurantConfiguration
from restaurant.api.serializers import (
    PaymentSerializer,
    NotificationSerializer,
    RestaurantConfigurationSerializer,
    RestaurantSerializer
)
from django_tenants.utils import tenant_context
from rest_framework import viewsets

from tenants.models import Client
from rest_framework import generics


class PaymentViewSet(viewsets.ModelViewSet):
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = Payment.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            return Payment.objects.filter(tenant=self.request.tenant)
    
class NotificationViewSet(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = Notification.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            return Notification.objects.filter(recipient=self.request.user)

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.tenant, recipient=self.request.user)

class RestaurantConfigurationViewSet(viewsets.ModelViewSet):
    serializer_class = RestaurantConfigurationSerializer
    permission_classes = [permissions.IsAdminUser] # Only for restaurant managers
    queryset = RestaurantConfiguration.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            return RestaurantConfiguration.objects.filter(tenant=self.request.tenant)

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.tenant) 

class RestaurantDetailView(generics.RetrieveAPIView):
    queryset = Client.objects.all()
    serializer_class = RestaurantSerializer

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        restaurant = self.get_object()
        context['favorite_count'] = restaurant.favorited_by.count()
        return context
