from django.contrib.auth.base_user import BaseUserManager
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from phonenumber_field.modelfields import PhoneNumberField

class UserRole(models.TextChoices):
    SUPERUSER = 'superuser', _('Superuser')
    MANAGER = 'manager', _('Manager')
    WAITER = 'waiter', _('Waiter')
    DELIVERY = 'delivery', _('Delivery')
    CUSTOMER = 'customer', _('Customer')

class CustomUserManager(BaseUserManager):
    def create_user(self, username, email=None, password=None, **extra_fields):
        extra_fields.setdefault('role', UserRole.CUSTOMER)
        return self._create_user(username, email, password, **extra_fields)
    
    def create_superuser(self, username, email=None, password=None, **extra_fields):
        extra_fields.setdefault('role', UserRole.SUPERUSER)
        return self._create_user(username, email, password, **extra_fields)
    
    def _create_user(self, username, email, password, **extra_fields):
        """
        Create and save a user with the given username, email, and password.
        """
        if not username:
            raise ValueError('The username must be set')
        
        # Corrected email normalization
        if email:
            email = self.normalize_email(email)
        else:
            email = None
            
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

class User(AbstractUser):
    role = models.CharField(
        max_length=20,
        choices=UserRole.choices,
        default=UserRole.CUSTOMER
    )
    phone_number = PhoneNumberField(blank=True, null=True)
    profile_picture = models.ImageField(
        upload_to='profile_pics/',
        null=True,
        blank=True
    )
    restaurant = models.ForeignKey(
        'tenants.Client',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='staff',
        verbose_name=_('Associated Restaurant')
    )
    favorite_restaurants = models.ManyToManyField(
        'tenants.Client',
        blank=True,
        related_name='favorited_by',
        verbose_name=_('Favorited Restaurants')
    )

    objects = CustomUserManager()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['restaurant', 'role'],
                condition=models.Q(role=UserRole.MANAGER),
                name='unique_restaurant_manager'
            )
        ]

    def save(self, *args, **kwargs):
        self.is_staff = self.role in [UserRole.SUPERUSER, UserRole.MANAGER]
        self.is_superuser = self.role == UserRole.SUPERUSER
        
        # Run validation after all models are loaded
        self.validate_relationships()
        super().save(*args, **kwargs)

    def validate_relationships(self):
        # Only import when needed to avoid circular imports
        from tenants.models import Client
        
        # Customer validation
        if self.role == UserRole.CUSTOMER and self.restaurant:
            raise ValidationError(
                {'restaurant': _('Customers cannot be associated with a restaurant.')}
            )
        
        # Staff validation
        staff_roles = [UserRole.MANAGER, UserRole.WAITER, UserRole.DELIVERY]
        if self.role in staff_roles and not self.restaurant:
            raise ValidationError(
                {'restaurant': _('Staff members must be associated with a restaurant.')}
            )

    @property
    def is_registered_customer(self):
        return self.role == UserRole.CUSTOMER and not self.restaurant
    
    def __str__(self):
        return f"{self.email} ({self.role})"






