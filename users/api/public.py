from django.urls import path
from rest_framework.authtoken import views
from .views import (
    CustomerRegistrationView,
    FavoriteRestaurantView,
    CustomerAuthTokenView,
    CustomerTokenRefreshView
)
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)

urlpatterns = [
    path('register/', CustomerRegistrationView.as_view(), name='customer_register'),
    path('favorite-restaurants/', FavoriteRestaurantView.as_view(), name='favorite_restaurants'),
    path('login/', TokenObtainPairView.as_view(), name='customer_login'),
    path('token/refresh/', CustomerTokenRefreshView.as_view(), name='token_refresh'),
]
