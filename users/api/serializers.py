from rest_framework import serializers 
from django.contrib.auth.password_validation import validate_password
from users.models import User, UserRole
from django.utils.text import gettext_lazy as _
import random
import string
from django.core.exceptions import ObjectDoesNotExist
from django.utils.crypto import get_random_string  # Add this import
from tenants.models import Client
from django.core.exceptions import ValidationError
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True,required=True)

    class Meta:
        model = User
        field = [
            'id', 'username', 'firstname', 'lastname', 
            'role', 'phone_number', 'password', 'password2'
        ]
        extra_kwargs = {
            'role': {'read_only': True}
        }
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.validationError({"password": "password fields did'nt match."})
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password2')
        user = User.objects.create_user(**validated_data)
        return user

class StaffUserSerializer(serializers.ModelSerializer):
    role = serializers.ChoiceField(
        choices=[UserRole.WAITER, UserRole.DELIVERY],
        error_messages={'invalid_choice': _("Role must be 'waiter' or 'delivery'")}
    )

    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'phone_number', 'role', 'username']
        read_only_fields = ['username', 'password']
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        tenant = self.context['request'].tenant
        
        # Remove role from validated_data to avoid duplicate
        role = validated_data.pop('role')
        
        # Get primary domain from tenant's domains
        try:
            primary_domain = tenant.domains.get(is_primary=True)
            domain_name = primary_domain.domain
        except ObjectDoesNotExist:
            # Fallback to first domain if no primary exists
            fallback_domain = tenant.domains.first()
            if not fallback_domain:
                raise serializers.ValidationError(
                    _("Tenant has no domains configured. Please add a domain first.")
                )
            domain_name = fallback_domain.domain

        # Generate unique username: role@domain_name
        base_username = f"{role}@{domain_name}"
        username = base_username
        counter = 1
        
        while User.objects.filter(username=username).exists():
            username = f"{role}{counter}@{domain_name}"
            counter += 1

        # Generate random password
        allowed_chars = 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789'
        password = get_random_string(length=12, allowed_chars=allowed_chars)
        
        # Create staff user
        user = User.objects.create_user(
            username=username,
            password=password,
            role=role,  # Pass role explicitly
            restaurant=tenant,
            **validated_data  # Contains other fields (without role)
        )
        
        # Temporarily store password to include in response
        user.temp_password = password
        return user

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        # Include generated password only during creation
        if hasattr(instance, 'temp_password'):
            rep['password'] = instance.temp_password
        return rep

class CustomerRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True,
        required=True,
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    confirm_password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )

    class Meta:
        model = User
        fields = ['email', 'username', 'password', 'confirm_password', 'phone_number']
        extra_kwargs = {
            'email': {'required': True},
            'username': {'required': True}
        }

    def validate(self, attrs):
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError(
                {"password": "Password fields didn't match."}
            )
        
        # Check if email already exists
        if User.objects.filter(email=attrs['email']).exists():
            raise serializers.ValidationError(
                {"email": "A user with this email already exists."}
            )
            
        # Check if username already exists
        if User.objects.filter(username=attrs['username']).exists():
            raise serializers.ValidationError(
                {"username": "A user with this username already exists."}
            )
            
        return attrs

    def create(self, validated_data):
        validated_data.pop('confirm_password')
        
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=validated_data['password'],
            phone_number=validated_data.get('phone_number', ''),
            role=UserRole.CUSTOMER
        )
        return user

class FavoriteRestaurantSerializer(serializers.Serializer):
    restaurant_id = serializers.IntegerField(required=True)

    def validate_restaurant_id(self, value):
        if not Restaurant.objects.filter(id=value).exists():
            raise serializers.ValidationError("Client not found")
        return value

class CustomerTokenObtainSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        
        # Add custom claims
        data['user'] = {
            'id': self.user.id,
            'email': self.user.email,
            'role': self.user.role,
            'username': self.user.username,
            'phone_number': str(self.user.phone_number) if self.user.phone_number else None,
        }
        return data

    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        # Add custom claims
        token['role'] = user.role
        token['email'] = user.email
        return token
