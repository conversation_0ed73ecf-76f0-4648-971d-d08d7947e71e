from rest_framework import viewsets, permissions, status, generics
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from users.models import User, UserRole
from tenants.models import Client
from rest_framework import generics
from rest_framework import serializers
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from .serializers import CustomerTokenObtainSerializer
from users.api.serializers import (
    UserSerializer, 
    StaffUserSerializer,
    CustomerRegistrationSerializer, 
    FavoriteRestaurantSerializer
)
from core.permissions import (
    IsRestaurantManager,
    IsRegisteredCustomer,
    IsPublicTenant
)
from tenants.models import Client

class CustomerAuthTokenView(TokenObtainPairView):
    serializer_class = CustomerTokenObtainSerializer

class CustomerTokenRefreshView(TokenRefreshView):
    pass

class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # For restaurant managers, only show their staff
        if self.request.user.is_manager:
            return User.objects.filter(restaurant=self.request.user.restaurant)
        # For superusers, show all users
        elif self.request.user.is_superuser:
            return super().get_queryset()
        # For others, only show their own profile
        return User.objects.get(id=self.request.user.id)

    def get_permissions(self):
        if self.action == 'register':
            return [permissions.AllowAny]
        return super().get_permisions()

    @action(detail=False, methods=['post'])
    def register(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        refresh = RefreshToken.for_user(user)
        return Response({
            "user": serializer.data,
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            }, status=status.HTTP_201_CREATED
        )
    
    @action(detail=False, methods=['get'])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

class StaffViewSet(viewsets.ModelViewSet):
    serializer_class = StaffUserSerializer
    permission_classes = [permissions.IsAuthenticated, IsRestaurantManager]
    queryset = User.objects.all()

    def get_queryset(self):
        # Return only waiter/delivery staff for current tenant
        return User.objects.filter(
            restaurant=self.request.tenant,
            role__in=[UserRole.WAITER, UserRole.DELIVERY]
        )

    def perform_create(self, serializer):
        serializer.save()

    def perform_destroy(self, instance):
        # Soft-delete instead of permanent deletion
        instance.is_active = False
        instance.save()

class CustomerRegistrationView(generics.CreateAPIView):
    serializer_class = CustomerRegistrationSerializer
    permission_classes = [IsPublicTenant]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as e:
            return Response(
                {'error': 'Validation failed', 'details': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            self.perform_create(serializer)
        except Exception as e:
            return Response(
                {'error': 'User creation failed', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
        return Response(
            {'message': 'Customer registered successfully'}, 
            status=status.HTTP_201_CREATED
        )

class FavoriteRestaurantView(generics.GenericAPIView):
    serializer_class = FavoriteRestaurantSerializer
    permission_classes = [permissions.IsAuthenticated, IsRegisteredCustomer]
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        restaurant_id = serializer.validated_data['restaurant_id']
        try:
            restaurant = Client.objects.get(id=restaurant_id)
        except Client.DoesNotExist:
            return Response(
                {'error': 'Restaurant not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        user = request.user
        if user.favorite_restaurants.filter(id=restaurant.id).exists():
            user.favorite_restaurants.remove(restaurant)
            action = 'removed'
        else:
            user.favorite_restaurants.add(restaurant)
            action = 'added'
        
        return Response(
            {
                'status': f'Restaurant {action} from favorites',
                'restaurant_id': restaurant.id,
                'is_favorited': action == 'added'
            },
            status=status.HTTP_200_OK
        )
