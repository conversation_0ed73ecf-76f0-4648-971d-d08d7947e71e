from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User

class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'role', 'is_active',)
    list_filter = ('role', 'restaurant', 'is_active',)
    search_fields = ('username', 'email', 'first_name', 'last_name',)
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal Info', {'fields': ('first_name', 'last_name', 'email', 'phonenumber', 'profile_picture')}),
        ('Roles and Permissions', {'fields': ('role', 'restaurant', 'favorite_restaurants', 'groups', 'user_permissions')}),
        ('Important Dates', {'fields': ('last_login', 'date_joined')}),
    )
    
    filter_horizontal = ('favorite_restaurants', 'groups', 'user_permissions')

admin.site.register(User, CustomUserAdmin)
