from .views import TenantAuthTokenView
from django.urls import path, include
from django.contrib import admin
from tenants.views import tenant_home
from restaurant.api.views import (
    PaymentViewSet,
    NotificationViewSet,
    RestaurantConfigurationViewSet,
)

from rest_framework.routers import DefaultRouter
from users.api.views import StaffViewSet

router = DefaultRouter()
router.register(r'staff', StaffViewSet, basename='staff')

urlpatterns = [
    path('', tenant_home, name='tenant-home'),
    path('login/', TenantAuthTokenView.as_view(), name='tenant-login'),
    path('payments/', PaymentViewSet.as_view({'get': 'list'})),
    path('notifications/', NotificationViewSet.as_view({'get': 'list'})),
    path('config/', RestaurantConfigurationViewSet.as_view({'get': 'list', 'put': 'update'})),
    path('staff/', StaffViewSet.as_view({'get': 'list', 'post': 'create'}), name='staff-list'),
    path('staff/<int:pk>/', StaffViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='staff-detail'),
    path('menu/', include('menu.api.urls')),
    path('orders/', include('orders.api.urls')),
] + router.urls
