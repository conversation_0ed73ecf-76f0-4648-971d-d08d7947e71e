from rest_framework.authtoken.views import ObtainAuth<PERSON>oken
from rest_framework.authtoken.models import Token
from rest_framework.response import Response
from django_tenants.utils import tenant_context
from rest_framework.response import Response
from rest_framework import status
from tenants.models import Client
from rest_framework.permissions import AllowAny

class TenantAuthTokenView(ObtainAuthToken):
    permission_classes = [AllowAny]
    
    def post(self, request, *args, **kwargs):
        # Get tenant from domain
        hostname = request.get_host().split(':')[0]
        try:
            tenant = Client.objects.get(domains__domain=hostname)
        except Client.DoesNotExist:
            return Response(
                {"error": "Tenant not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )

        # Switch to tenant schema
        with tenant_context(tenant):
            serializer = self.serializer_class(
                data=request.data,
                context={'request': request}
            )
            
            if not serializer.is_valid():
                return Response(
                    serializer.errors, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            user = serializer.validated_data['user']
            token, created = Token.objects.get_or_create(user=user)
            
            return Response({
                'token': token.key,
                'user_id': user.pk,
                'email': user.email
            })


