from django.db import models
from django_tenants.models import TenantMixin, DomainMixin
from django.utils.translation import gettext_lazy as _
# from django.contrib.gis.db import models as gis_models 

class Tier(models.TextChoices):
    BASIC = 'basic', _('Basic')
    STANDARD = 'standard', _('Standard')
    PREMIUM = 'premium', _('Premium')

class Client(TenantMixin):
    restaurant_name = models.CharField(max_length=100)
    schema_name = models.CharField(max_length=63, unique=True)
    paid_until = models.DateField()
    on_trial = models.BooleanField(default=True)
    created_on = models.DateField(auto_now_add=True)
    tier = models.CharField(
        max_length=20,
        choices=Tier.choices,
        default=Tier.BASIC
    )

    # Manager details
    manager_firstname = models.CharField(max_length=100)
    manager_lastname = models.CharField(max_length=100)
    manager_phone = models.CharField(max_length=100)
    manager_email = models.EmailField(unique=True)

    # Restaurant Details
    phone_number = models.CharField(max_length=20)
    address = models.TextField()
    latitude = models.FloatField(blank=True, null=True)
    longitude = models.FloatField(blank=True, null=True)
    """
    location = gis_models.PointField(
        geography=True,
        srid=4326,
        null=True,
        blank=True
    )
    """
    description = models.TextField()
    logo = models.ImageField(upload_to='tenant_logos/', null=True, blank=True)
    banner = models.ImageField(upload_to='tenant_banners/', null=True, blank=True)
    active = models.BooleanField(default=False)  # Will be set to True on approval

    # Branding fields
    primary_color = models.CharField(max_length=7, default='#FF0000', blank=True, null=True)
    secondary_color = models.CharField(max_length=7, default='#00FF00', blank=True, null=True)

    auto_schema_create = True

    def __str__(self):
        return self.restaurant_name

    def save(self, *args, **kwargs):
        """Activate domain when tenant is activated and set trial period if applicable"""
        if self.on_trial and not self.paid_until:
            from django.utils.timezone import now
            from datetime import timedelta
            from core.settings import DEFAULT_TRIAL_PERIOD

            self.paid_until = now() + timedelta(days=DEFAULT_TRIAL_PERIOD)

        if self.active and not self._state.adding:
            Domain.objects.filter(tenant=self, is_primary=True).update(is_active=True)

        super().save(*args, **kwargs)

class Domain(DomainMixin):
    is_active = models.BooleanField(default=False)
    pass

class PendingClient(models.Model):
    restaurant_name = models.CharField(max_length=100)
    schema_name = models.CharField(max_length=63, unique=True)
    manager_email = models.EmailField()
    manager_firstname = models.CharField(max_length=100)
    manager_lastname = models.CharField(max_length=100)
    manager_phone = models.CharField(max_length=100)
    restaurant_phone = models.CharField(max_length=100)
    latitude = models.FloatField()
    longitude = models.FloatField()
    """
    location = gis_models.PointField(
        geography=True,
        srid=4326,
        null=True,
        blank=True
    )
    """
    description = models.TextField(blank=True)
    address = models.TextField()
    logo = models.ImageField(upload_to='tenant_logos/', blank=True, null=True)
    banner = models.ImageField(upload_to='tenant_banners/', blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    approved = models.BooleanField(default=False)  # Track approval status
    active = models.BooleanField(default=False)
    trial_end_date = models.DateField(null=True, blank=True)

    class Meta:
        verbose_name = _('Pending Restaurant')
        verbose_name_plural = _('Pending Restaurants')

    def __str__(self):
        return self.restaurant_name
    
    def save(self, *args, **kwargs):
        """Automatically activate domain when approved"""
        if self.active and not self._state.adding:
            Domain.objects.filter(tenant=self, is_primary=True).update(is_active=True)
        super().save(*args, **kwargs)

    def save(self, *args, **kwargs):
        """Automatically activate domain when approved and generate unique schema_name"""
        
        # Generate unique schema_name if not set
        if not self.schema_name:
            base_schema = slugify(self.restaurant_name).replace('-', '_')[:63].lower()
            self.schema_name = base_schema
            counter = 1
            while PendingClient.objects.filter(schema_name=self.schema_name).exists() or \
                Client.objects.filter(schema_name=self.schema_name).exists():
                self.schema_name = f"{base_schema}_{counter}"
                counter += 1

        # Automatically activate domain when approved
        if self.active and not self._state.adding:
            Domain.objects.filter(tenant=self, is_primary=True).update(is_active=True)

        super().save(*args, **kwargs)
