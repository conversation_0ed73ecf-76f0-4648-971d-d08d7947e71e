from django.utils import timezone
from django_tenants.utils import tenant_context

def check_trial_expiration():
    from tenants.models import Restaurant
    expired = Restaurant.objects.filter(
        trial_end_date__lt=timezone.now().date(),
        tier='trial'
    )
    
    for tenant in expired:
        with tenant_context(tenant):
            # Downgrade to basic tier
            tenant.tier = 'basic'
            tenant.save()
            # Notify restaurant manager
            manager = tenant.staff.filter(role=UserRole.MANAGER).first()
            if manager:
                send_trial_expired_email(manager.email)
