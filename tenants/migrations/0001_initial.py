# Generated by Django 5.1.9 on 2025-06-05 10:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('restaurant_name', models.CharField(max_length=100)),
                ('schema_name', models.CharField(max_length=63, unique=True)),
                ('paid_until', models.DateField()),
                ('on_trial', models.Bo<PERSON>anField(default=True)),
                ('created_on', models.DateField(auto_now_add=True)),
                ('tier', models.CharField(choices=[('basic', 'Basic'), ('standard', 'Standard'), ('premium', 'Premium')], default='basic', max_length=20)),
                ('manager_firstname', models.Char<PERSON><PERSON>(max_length=100)),
                ('manager_lastname', models.Char<PERSON>ield(max_length=100)),
                ('manager_phone', models.CharField(max_length=100)),
                ('manager_email', models.EmailField(max_length=254, unique=True)),
                ('phone_number', models.CharField(max_length=20)),
                ('address', models.TextField()),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.FloatField(blank=True, null=True)),
                ('description', models.TextField()),
                ('logo', models.ImageField(blank=True, null=True, upload_to='tenant_logos/')),
                ('banner', models.ImageField(blank=True, null=True, upload_to='tenant_banners/')),
                ('active', models.BooleanField(default=False)),
                ('primary_color', models.CharField(blank=True, default='#FF0000', max_length=7, null=True)),
                ('secondary_color', models.CharField(blank=True, default='#00FF00', max_length=7, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PendingClient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('restaurant_name', models.CharField(max_length=100)),
                ('schema_name', models.CharField(max_length=63, unique=True)),
                ('manager_email', models.EmailField(max_length=254)),
                ('manager_firstname', models.CharField(max_length=100)),
                ('manager_lastname', models.CharField(max_length=100)),
                ('manager_phone', models.CharField(max_length=100)),
                ('restaurant_phone', models.CharField(max_length=100)),
                ('latitude', models.FloatField()),
                ('longitude', models.FloatField()),
                ('description', models.TextField(blank=True)),
                ('address', models.TextField()),
                ('logo', models.ImageField(blank=True, null=True, upload_to='tenant_logos/')),
                ('banner', models.ImageField(blank=True, null=True, upload_to='tenant_banners/')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('approved', models.BooleanField(default=False)),
                ('active', models.BooleanField(default=False)),
                ('trial_end_date', models.DateField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Pending Restaurant',
                'verbose_name_plural': 'Pending Restaurants',
            },
        ),
        migrations.CreateModel(
            name='Domain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(db_index=True, max_length=253, unique=True)),
                ('is_primary', models.BooleanField(db_index=True, default=True)),
                ('is_active', models.BooleanField(default=False)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='domains', to='tenants.client')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
