from rest_framework.generics import CreateAP<PERSON><PERSON>iew
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token
from django_tenants.utils import tenant_context
from tenants.models import PendingClient, Client
from users.models import User
from .serializers import PendingClientSerializer
from rest_framework.permissions import AllowAny

class RegisterRestaurantView(CreateAPIView):
    queryset = PendingClient.objects.all()
    serializer_class = PendingClientSerializer
    permission_classes = [AllowAny]


class ManagerAuthView(APIView):
    """
    Manager authentication endpoint that accepts restaurant_id, username, and password.
    This endpoint allows managers to authenticate using their restaurant's schema name.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        restaurant_id = request.data.get('restaurant_id')
        username = request.data.get('username')
        password = request.data.get('password')

        if not all([restaurant_id, username, password]):
            return Response(
                {"error": "restaurant_id, username, and password are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Find the tenant by schema name (restaurant_id)
            tenant = Client.objects.get(schema_name=restaurant_id)
        except Client.DoesNotExist:
            return Response(
                {"error": "Restaurant not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Switch to tenant schema and authenticate
        with tenant_context(tenant):
            try:
                user = User.objects.get(username=username)
                if user.check_password(password) and user.role == 'manager':
                    # Create or get token
                    token, _ = Token.objects.get_or_create(user=user)

                    return Response({
                        'token': token.key,
                        'user_id': user.pk,
                        'email': user.email,
                        'restaurant_name': tenant.restaurant_name,
                        'schema_name': tenant.schema_name
                    })
                else:
                    return Response(
                        {"error": "Invalid credentials or user is not a manager"},
                        status=status.HTTP_401_UNAUTHORIZED
                    )
            except User.DoesNotExist:
                return Response(
                    {"error": "Invalid credentials"},
                    status=status.HTTP_401_UNAUTHORIZED
                )

