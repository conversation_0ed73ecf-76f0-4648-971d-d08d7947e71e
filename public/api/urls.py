from django.urls import path, include
from public.api.views import RegisterRestaurantView, ManagerAuthView
from tenants.views import tenant_home


urlpatterns = [
    path('', tenant_home, name='tenant-home'),
    path('register-restaurant/', RegisterRestaurantView.as_view(), name='restaurant-register'),
    path('manager-auth/', ManagerAuthView.as_view(), name='manager-auth'),
    path('customer/', include('users.api.public')),
]
