from django.utils.text import slugify
from rest_framework import serializers
from tenants.models import PendingClient, Client

class PendingClientSerializer(serializers.ModelSerializer):
    class Meta:
        model = PendingClient
        fields = [
            'restaurant_name', 'manager_email',
            'manager_firstname', 'manager_lastname', 'manager_phone',
            'restaurant_phone', 'longitude', 'latitude', 'description',
            'address', 'logo', 'banner'
        ]

    def create(self, validated_data):
        # Generate unique schema_name
        base_schema = slugify(validated_data['restaurant_name']).replace('-', '_')[:63].lower()
        schema_name = base_schema
        counter = 1
        
        # Ensure uniqueness across both PendingClient and Client
        while PendingClient.objects.filter(schema_name=schema_name).exists() or \
              Client.objects.filter(schema_name=schema_name).exists():
            schema_name = f"{base_schema}_{counter}"
            counter += 1
        
        validated_data['schema_name'] = schema_name
        return super().create(validated_data)
