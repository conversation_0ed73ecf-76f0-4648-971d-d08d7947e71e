# EasyEats Complete Project Workplan - September 2025

## Project Overview

This is a comprehensive workplan for completing the EasyEats restaurant management platform by **Friday, September 13, 2025**. The project consists of a Django multitenant backend and a Next.js frontend, with separate interfaces for customers, managers, and admins.

## Current Project Status (As of September 10, 2025)

### ✅ COMPLETED TASKS

#### Backend Infrastructure

- [x] Django multitenant architecture with django-tenants
- [x] User authentication system with role-based access (Customer, Manager, Admin, Waiter, Delivery)
- [x] Database models for all entities (Users, Restaurants, Menu, Orders, Payments)
- [x] API endpoints for menu management (Categories, MenuItems) - **FULLY FUNCTIONAL**
- [x] API endpoints for user management and authentication
- [x] API endpoints for orders and payments
- [x] Tenant-specific authentication system with subdomain routing
- [x] Restaurant registration and approval workflow
- [x] CORS configuration for frontend integration
- [x] Manager authentication endpoint (`/api/public/manager-auth/`)
- [x] Customer authentication endpoint (`/api/public/customer/login/`)
- [x] Staff management API endpoints
- [x] Restaurant configuration and notification systems

#### Frontend Infrastructure

- [x] Next.js 14 application with App Router
- [x] Tailwind CSS styling with custom components (shadcn/ui)
- [x] Role-based dashboard layouts (Customer, Manager, Admin)
- [x] Authentication pages (Login, Register, Logout)
- [x] Responsive design with mobile support
- [x] Reusable UI components (Button, Card, Input, Dialog, etc.)
- [x] Dashboard layout component with sidebar navigation
- [x] Data table component for user management

#### Fully Completed Features

- [x] **Manager Menu Management** - Complete CRUD operations for categories and menu items
  - Categories: Create, Read, Update, Delete with ordering
  - Menu Items: Full management with dietary restrictions, pricing, descriptions
  - Real-time API integration with tenant-specific endpoints
  - Loading states and error handling implemented
- [x] **Manager Authentication System** - Tenant-specific login with schema routing
- [x] **Landing Page** - Partial API integration with fallback to mock data
- [x] **Manager Restaurant Management** - Basic restaurant info display and management

#### Partially Completed Features

- [x] Customer authentication (login works, but dashboard needs API integration)
- [x] Manager dashboard layout (structure complete, needs real data integration)
- [x] Admin dashboard structure (UI complete, needs backend integration)

### 📝 RECENT CHANGES MADE (Based on Git History)

#### September 6, 2025 - "fetching clients from backend"

- [x] **Role-based login redirection** implemented in `/web/src/app/auth/login/page.jsx`
  - Users are redirected based on their role (customer, manager, superuser)
  - User data stored in localStorage for session management
- [x] **Manager Menu Management Page** created at `/web/src/app/manager/dashboard/menu/page.jsx`
  - Complete UI for managing menu categories and items
  - Full CRUD operations with backend API integration
  - Real-time data fetching from tenant-specific endpoints
- [x] **Landing Page API Integration** updated in `/web/src/app/page.js`
  - Fetches featured restaurants from backend API
  - Graceful fallback to mock data if API fails
  - Loading states and error handling implemented
- [x] **Manager Dashboard Layout** updated with menu management link

#### August 13, 2025 - Project Structure Setup

- [x] **Mobile App Initialization** - Flutter project created (separate from web)
- [x] **Project Reorganization** - Web frontend moved to `/web` directory
- [x] **Cross-platform Structure** - Prepared for mobile, web, and admin apps

#### June 12, 2025 - "basic structure setup"

- [x] **UI Component Library** - Implemented shadcn/ui components
  - Button, Card, Input, Label, Dialog, Select, Badge, Tabs
  - App Navbar and Sidebar components
  - Data Table with sorting and filtering
- [x] **Dashboard Layouts** - Created for Admin, Manager, and Customer roles
- [x] **Authentication UI** - Login forms and pages
- [x] **Global Styling** - Tailwind CSS configuration with custom themes

### 🔄 IN PROGRESS / NEEDS COMPLETION

#### Frontend Pages Still Using Mock Data

- [ ] Customer dashboard (user profile, recent orders, recommendations)
- [ ] Customer restaurants list and details
- [ ] Customer order history
- [ ] Manager dashboard home (stats and recent activity)
- [ ] Manager users management
- [ ] Admin dashboard (users, requests, restaurants)
- [ ] Admin approval/rejection workflow

#### Missing API Endpoints

- [ ] Featured restaurants endpoint for landing page
- [ ] Customer order history endpoint
- [ ] Restaurant statistics and analytics endpoints
- [ ] Admin user management endpoints
- [ ] Restaurant approval/rejection endpoints

## WORKPLAN BY DAY

### Wednesday, September 11, 2025

#### Morning (9:00 AM - 12:00 PM)

**Priority 1: Complete Missing Backend API Endpoints**

1. **Create Featured Restaurants API** (30 minutes)

   - Add endpoint to public/api/views.py for featured restaurants
   - Update public/api/urls.py to include the endpoint
   - Test with frontend landing page

2. **Customer Order History API** (45 minutes)

   - Create order history endpoint in orders/api/views.py
   - Add customer-specific filtering
   - Include restaurant information in response

3. **Restaurant Statistics API** (45 minutes)
   - Create analytics endpoints for manager dashboard
   - Include metrics: total orders, revenue, popular items
   - Add recent activity feed

#### Afternoon (1:00 PM - 5:00 PM)

**Priority 2: Complete Customer-Facing Pages**

4. **Customer Dashboard Integration** (90 minutes)

   - Replace mock data with API calls
   - Fetch user profile from backend
   - Integrate recent orders and recommendations
   - Add loading and error states

5. **Customer Restaurants Pages** (90 minutes)
   - Update restaurants list page to fetch from backend
   - Update restaurant details page with real data
   - Implement search and filtering functionality
   - Add favorites functionality

### Thursday, September 12, 2025

#### Morning (9:00 AM - 12:00 PM)

**Priority 3: Complete Manager Dashboard**

6. **Manager Dashboard Home** (90 minutes)

   - Integrate statistics API
   - Replace hardcoded metrics with real data
   - Add recent activity feed
   - Implement real-time updates

7. **Manager Users Management** (90 minutes)
   - Complete staff management CRUD operations
   - Add role assignment functionality
   - Implement user invitation system

#### Afternoon (1:00 PM - 5:00 PM)

**Priority 4: Admin Dashboard and Approval System**

8. **Admin User Management** (90 minutes)

   - Create admin API endpoints for user management
   - Implement user listing, editing, and role management
   - Add user search and filtering

9. **Restaurant Approval System** (90 minutes)
   - Complete pending restaurant requests API
   - Implement approval/rejection workflow
   - Add email notifications for status changes
   - Update admin dashboard with real data

### Friday, September 13, 2025

#### Morning (9:00 AM - 12:00 PM)

**Priority 5: Testing and Bug Fixes**

10. **End-to-End Testing** (120 minutes)

    - Test all user flows (Customer, Manager, Admin)
    - Verify API integrations work correctly
    - Test authentication and authorization
    - Fix any critical bugs found

11. **Performance Optimization** (60 minutes)
    - Optimize API queries
    - Add proper error handling
    - Implement loading states consistently

#### Afternoon (1:00 PM - 5:00 PM)

**Priority 6: Final Polish and Documentation**

12. **UI/UX Polish** (90 minutes)

    - Ensure consistent styling across all pages
    - Add proper loading animations
    - Improve error messages and user feedback
    - Test responsive design on different devices

13. **Documentation and Deployment Prep** (90 minutes)
    - Update API documentation
    - Create deployment checklist
    - Test production build
    - Final code review and cleanup

## DETAILED TASK BREAKDOWN

### Backend API Tasks

#### 1. Featured Restaurants Endpoint

```python
# In public/api/views.py
class FeaturedRestaurantsView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        featured_restaurants = Client.objects.filter(
            active=True,
            featured=True
        )[:6]
        # Return serialized data
```

#### 2. Customer Order History

```python
# In orders/api/views.py
class CustomerOrderHistoryView(ListAPIView):
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(
            customer=self.request.user
        ).select_related('restaurant')
```

#### 3. Restaurant Analytics

```python
# In restaurant/api/views.py
class RestaurantAnalyticsView(APIView):
    permission_classes = [IsAuthenticated, IsManagerOrReadOnly]

    def get(self, request):
        # Calculate stats for current tenant
        # Return metrics and recent activity
```

### Frontend Integration Tasks

#### 1. Customer Dashboard

- Replace mock user data with API call to `/api/customer/profile/`
- Fetch recent orders from `/api/customer/orders/recent/`
- Implement recommendations from `/api/customer/recommendations/`

#### 2. Manager Dashboard

- Integrate statistics from `/api/tenant/analytics/`
- Replace hardcoded metrics with real data
- Add real-time activity feed

#### 3. Admin Dashboard

- Fetch pending requests from `/api/admin/pending-restaurants/`
- Implement approval/rejection actions
- Add user management functionality

## RISK MITIGATION

### High Priority Risks

1. **API Integration Issues**: Test each endpoint immediately after creation
2. **Authentication Problems**: Verify token handling across all user types
3. **Time Constraints**: Focus on core functionality first, polish later

### Contingency Plans

1. **If backend APIs are delayed**: Use enhanced mock data with proper structure
2. **If frontend integration fails**: Implement basic functionality first, enhance later
3. **If testing reveals major issues**: Prioritize critical user flows

## SUCCESS CRITERIA

### Minimum Viable Product (MVP)

- [ ] All user types can log in and access their dashboards
- [ ] Customers can browse restaurants and view order history
- [ ] Managers can manage menus and view basic statistics
- [ ] Admins can approve restaurant registrations

### Ideal Completion

- [ ] All pages use real backend data
- [ ] Proper error handling and loading states
- [ ] Responsive design works on all devices
- [ ] Performance is acceptable for production use

## TEAM COORDINATION

### Daily Standups

- **9:00 AM**: Review previous day's progress
- **5:00 PM**: Plan next day's priorities

### Communication Channels

- Use commit messages to track progress
- Document any blockers immediately
- Test integrations as soon as both frontend and backend are ready

## TECHNICAL IMPLEMENTATION DETAILS

### API Endpoints to Create

#### 1. Featured Restaurants (`/api/public/featured-restaurants/`)

```python
# File: public/api/views.py
class FeaturedRestaurantsView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        restaurants = Client.objects.filter(
            active=True
        ).annotate(
            avg_rating=Avg('reviews__rating'),
            review_count=Count('reviews')
        )[:6]

        data = []
        for restaurant in restaurants:
            data.append({
                'id': restaurant.id,
                'name': restaurant.restaurant_name,
                'cuisine': 'Various',  # Add cuisine field to model
                'rating': restaurant.avg_rating or 4.5,
                'reviews': restaurant.review_count,
                'distance': '1.2 km',  # Calculate based on user location
                'image': restaurant.banner.url if restaurant.banner else None,
                'deliveryTime': '25-35 min',
                'featured': True
            })

        return Response(data)
```

#### 2. Customer Profile (`/api/customer/profile/`)

```python
# File: users/api/views.py
class CustomerProfileView(RetrieveUpdateAPIView):
    serializer_class = CustomerProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user
```

#### 3. Restaurant Analytics (`/api/tenant/analytics/`)

```python
# File: restaurant/api/views.py
class RestaurantAnalyticsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        tenant = request.tenant

        # Calculate metrics
        total_orders = Order.objects.filter(
            menu_item__restaurant=tenant
        ).count()

        total_revenue = Order.objects.filter(
            menu_item__restaurant=tenant,
            is_paid=True
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or 0

        menu_items_count = MenuItem.objects.filter(
            restaurant=tenant
        ).count()

        staff_count = User.objects.filter(
            restaurant=tenant,
            role__in=['waiter', 'delivery']
        ).count()

        return Response({
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'menu_items_count': menu_items_count,
            'staff_count': staff_count,
            'recent_activity': self.get_recent_activity(tenant)
        })
```

### Frontend Integration Patterns

#### 1. API Utility Function

```javascript
// File: web/src/lib/api.js
export const apiCall = async (endpoint, options = {}) => {
  const token = localStorage.getItem("accessToken");
  const baseUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

  const config = {
    headers: {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${baseUrl}${endpoint}`, config);

  if (!response.ok) {
    throw new Error(`API call failed: ${response.status}`);
  }

  return response.json();
};
```

#### 2. Custom Hooks for Data Fetching

```javascript
// File: web/src/hooks/useApi.js
import { useState, useEffect } from "react";
import { apiCall } from "@/lib/api";

export const useApi = (endpoint, dependencies = []) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await apiCall(endpoint);
        setData(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, dependencies);

  return { data, loading, error, refetch: () => fetchData() };
};
```

### Database Migrations Needed

#### 1. Add Featured Flag to Client Model

```python
# File: tenants/migrations/XXXX_add_featured_field.py
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='client',
            name='featured',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='client',
            name='cuisine_type',
            field=models.CharField(max_length=50, default='Various'),
        ),
    ]
```

### Testing Checklist

#### Backend API Testing

- [ ] All endpoints return correct HTTP status codes
- [ ] Authentication works for protected endpoints
- [ ] Tenant isolation is maintained
- [ ] Data serialization is correct
- [ ] Error handling returns proper error messages

#### Frontend Integration Testing

- [ ] Loading states display correctly
- [ ] Error states show user-friendly messages
- [ ] Data updates reflect in UI immediately
- [ ] Authentication redirects work properly
- [ ] Responsive design works on mobile

#### End-to-End Testing Scenarios

1. **Customer Journey**

   - [ ] Register new account
   - [ ] Browse restaurants
   - [ ] View restaurant details
   - [ ] Check order history

2. **Manager Journey**

   - [ ] Login to manager dashboard
   - [ ] View restaurant statistics
   - [ ] Manage menu items
   - [ ] Manage staff users

3. **Admin Journey**
   - [ ] Login to admin dashboard
   - [ ] View pending restaurant requests
   - [ ] Approve/reject restaurants
   - [ ] Manage all users

### Deployment Considerations

#### Environment Variables

```bash
# Backend (.env)
SECRET_KEY=your-secret-key
DB_NAME=dinepulse
DB_USER=thom
DB_PASSWORD=test1234
DB_HOST=localhost
DB_PORT=5432
EMAIL_HOST_USER=your-email
EMAIL_HOST_PASSWORD=your-password

# Frontend (.env.local)
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000/
BACKEND_URL=http://localhost:8000/
```

#### Production Checklist

- [ ] Debug mode disabled in Django
- [ ] CORS settings configured for production domain
- [ ] Static files properly served
- [ ] Database migrations applied
- [ ] SSL certificates configured
- [ ] Environment variables set

---

**Last Updated**: September 10, 2025
**Target Completion**: September 13, 2025, 5:00 PM
**Status**: In Progress
