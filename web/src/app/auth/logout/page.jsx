"use client";

import { useState, useEffect } from "react";
import { LogOut, Check } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useRouter } from "next/navigation";

export default function LogoutPage() {
  const [isLoggingOut, setIsLoggingOut] = useState(true);
  const [isComplete, setIsComplete] = useState(false);

  const router = useRouter();

  useEffect(() => {
    console.log("Clearing tokens from storage...");

    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("managerToken");
    localStorage.removeItem("managerId");
    localStorage.removeItem("managerEmail");
    localStorage.removeItem("restaurantId");
    localStorage.removeItem("restaurantName");
    localStorage.removeItem("schemaName");

    console.log("accessToken removed");
    console.log("refreshToken removed");

    setIsLoggingOut(false);
    setIsComplete(true);

    // Redirect to login page after showing success
    const logoutTimer = setTimeout(() => {
      router.push("/");
      console.log("Redirecting to login page...");
    }, 2000);

    return () => clearTimeout(logoutTimer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-500 via-orange-500 to-orange-600 flex items-center justify-center px-4">
      <Card className="w-full max-w-md shadow-2xl border-0">
        <CardContent className="p-8 text-center">
          <div className="mb-8">
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-red-100 to-orange-100 rounded-full flex items-center justify-center">
              {isLoggingOut ? (
                <LogOut className="h-10 w-10 text-red-600 animate-pulse" />
              ) : (
                <Check className="h-10 w-10 text-green-600 animate-bounce" />
              )}
            </div>

            {isLoggingOut ? (
              <>
                <h2 className="text-2xl font-bold text-gray-900 mb-3">
                  Logging you out...
                </h2>

                {/* Loading Animation */}
                <div className="flex justify-center mb-6">
                  <div className="flex space-x-2">
                    <div
                      className="w-3 h-3 bg-red-500 rounded-full animate-bounce"
                      style={{ animationDelay: "0ms" }}
                    ></div>
                    <div
                      className="w-3 h-3 bg-orange-500 rounded-full animate-bounce"
                      style={{ animationDelay: "150ms" }}
                    ></div>
                    <div
                      className="w-3 h-3 bg-red-500 rounded-full animate-bounce"
                      style={{ animationDelay: "300ms" }}
                    ></div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                  <div
                    className="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full animate-pulse"
                    style={{ width: "70%" }}
                  ></div>
                </div>
              </>
            ) : (
              <>
                <h2 className="text-2xl font-bold text-gray-900 mb-3">
                  Logged out!
                </h2>
                {/* Success Checkmark Animation */}
                <div className="w-16 h-16 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center animate-scale-in">
                    <Check className="h-6 w-6 text-white" />
                  </div>
                </div>

                <p className="text-sm text-gray-500">Redirecting ...</p>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-white rounded-full blur-xl"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-white rounded-full blur-lg"></div>
        <div className="absolute bottom-1/4 left-1/3 w-40 h-40 bg-white rounded-full blur-2xl"></div>
      </div>

      <style jsx>{`
        @keyframes scale-in {
          0% {
            transform: scale(0);
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
          }
        }

        .animate-scale-in {
          animation: scale-in 0.6s ease-out;
        }
      `}</style>
    </div>
  );
}
