"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  MapPin,
  Users,
  Star,
  Search,
  Eye,
  Store,
  Clock,
  Phone,
  Mail,
  Settings,
  TrendingUp,
  DollarSign,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export default function RestaurantsPage() {
  const [loading, setLoading] = useState(true);
  const [restaurantInfo, setRestaurantInfo] = useState({});
  const [config, setConfig] = useState({});
  const [staff, setStaff] = useState([]);
  const [menuStats, setMenuStats] = useState({ categories: 0, items: 0 });

  const getAuthHeaders = () => {
    const token =
      localStorage.getItem("managerToken") ||
      localStorage.getItem("accessToken") ||
      localStorage.getItem("adminToken");

    return {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };
  };

  const getApiUrl = (endpoint) => {
    const schemaName = localStorage.getItem("schemaName");

    if (!schemaName) {
      console.error("No schema name found in localStorage");
      toast.error("Authentication error: Please log in again");
      return null;
    }

    const subdomain = schemaName.replace(/_/g, "-");
    const baseUrl = `http://${subdomain}.localhost:8000/`;

    return `${baseUrl}api/tenant/${endpoint}`;
  };

  useEffect(() => {
    fetchRestaurantData();
  }, []);

  const fetchRestaurantData = async () => {
    try {
      // Get restaurant info from localStorage
      const restaurantName = localStorage.getItem("restaurantName");
      const schemaName = localStorage.getItem("schemaName");

      setRestaurantInfo({
        restaurant_name: restaurantName || "Restaurant",
        schema_name: schemaName,
      });

      // Try to fetch restaurant details (logo, banner, etc.)
      try {
        const restaurantUrl = getApiUrl("restaurant/");
        if (restaurantUrl) {
          const restaurantResponse = await fetch(restaurantUrl, {
            headers: getAuthHeaders(),
          });
          if (restaurantResponse.ok) {
            const restaurantData = await restaurantResponse.json();
            setRestaurantInfo((prev) => ({
              ...prev,
              ...restaurantData,
            }));
          }
        }
      } catch (error) {
        console.log("Restaurant details endpoint not available");
      }

      // Fetch configuration
      const configUrl = getApiUrl("config/");
      if (configUrl) {
        const configResponse = await fetch(configUrl, {
          headers: getAuthHeaders(),
        });
        if (configResponse.ok) {
          const configData = await configResponse.json();
          const configuration = configData.results?.[0] || configData[0] || {};
          setConfig(configuration);
        }
      }

      // Fetch staff count
      const staffUrl = getApiUrl("staff/");
      if (staffUrl) {
        const staffResponse = await fetch(staffUrl, {
          headers: getAuthHeaders(),
        });
        if (staffResponse.ok) {
          const staffData = await staffResponse.json();
          setStaff(staffData.results || staffData);
        }
      }

      // Fetch menu statistics
      const categoriesUrl = getApiUrl("menu/categories/");
      const itemsUrl = getApiUrl("menu/menu-items/");

      if (categoriesUrl && itemsUrl) {
        const [categoriesResponse, itemsResponse] = await Promise.all([
          fetch(categoriesUrl, { headers: getAuthHeaders() }),
          fetch(itemsUrl, { headers: getAuthHeaders() }),
        ]);

        if (categoriesResponse.ok && itemsResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          const itemsData = await itemsResponse.json();

          setMenuStats({
            categories: (categoriesData.results || categoriesData).length,
            items: (itemsData.results || itemsData).length,
          });
        }
      }
    } catch (error) {
      console.error("Network error fetching restaurant data:", error);
      toast.error("Failed to fetch restaurant data");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading restaurant information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Restaurant Overview Card */}
        <Card className="mb-8">
          {/* Restaurant Banner */}
          {restaurantInfo.banner && (
            <div className="w-full h-32 overflow-hidden rounded-t-lg">
              <img
                src={restaurantInfo.banner}
                alt="Restaurant Banner"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          <CardHeader>
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden border-2 border-white shadow-sm">
                {restaurantInfo.logo ? (
                  <img
                    src={restaurantInfo.logo}
                    alt="Restaurant Logo"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Store className="h-8 w-8 text-gray-400" />
                )}
              </div>
              <div>
                <CardTitle className="text-2xl">
                  {restaurantInfo.restaurant_name}
                </CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700"
                  >
                    Active
                  </Badge>
                  <span>Schema: {restaurantInfo.schema_name}</span>
                </CardDescription>
                {restaurantInfo.description && (
                  <p className="text-sm text-gray-600 mt-2 max-w-md">
                    {restaurantInfo.description}
                  </p>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Staff Count */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{staff.length}</p>
                  <p className="text-sm text-gray-600">Staff Members</p>
                </div>
              </div>

              {/* Menu Categories */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                  <Store className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{menuStats.categories}</p>
                  <p className="text-sm text-gray-600">Categories</p>
                </div>
              </div>

              {/* Menu Items */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg bg-orange-100 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{menuStats.items}</p>
                  <p className="text-sm text-gray-600">Menu Items</p>
                </div>
              </div>

              {/* Operating Hours */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                  <Clock className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-semibold">
                    {config.opening_time && config.closing_time
                      ? `${config.opening_time} - ${config.closing_time}`
                      : "Not set"}
                  </p>
                  <p className="text-sm text-gray-600">Operating Hours</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Configuration Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Delivery Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Delivery Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Delivery Radius</span>
                  <span className="font-semibold">
                    {config.delivery_radius_km || 0} km
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Delivery Fee</span>
                  <span className="font-semibold">
                    MWK {config.delivery_fee || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Minimum Order</span>
                  <span className="font-semibold">
                    MWK {config.minimum_order_amount || 0}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Payment Methods
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Online Payment</span>
                  <Badge
                    variant={
                      config.enable_online_payment ? "default" : "secondary"
                    }
                  >
                    {config.enable_online_payment ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    Cash on Delivery
                  </span>
                  <Badge
                    variant={
                      config.enable_cash_on_delivery ? "default" : "secondary"
                    }
                  >
                    {config.enable_cash_on_delivery ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Notifications
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    Email Confirmations
                  </span>
                  <Badge
                    variant={
                      config.order_confirmation_email ? "default" : "secondary"
                    }
                  >
                    {config.order_confirmation_email ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    SMS Confirmations
                  </span>
                  <Badge
                    variant={
                      config.order_confirmation_sms ? "default" : "secondary"
                    }
                  >
                    {config.order_confirmation_sms ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Manage your restaurant settings and operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link href="/manager/dashboard/menu">
                <Button
                  variant="outline"
                  className="w-full h-20 flex flex-col gap-2"
                >
                  <Store className="h-6 w-6" />
                  <span>Manage Menu</span>
                </Button>
              </Link>
              <Link href="/manager/dashboard/users">
                <Button
                  variant="outline"
                  className="w-full h-20 flex flex-col gap-2"
                >
                  <Users className="h-6 w-6" />
                  <span>Manage Staff</span>
                </Button>
              </Link>
              <Link href="/manager/dashboard/settings">
                <Button
                  variant="outline"
                  className="w-full h-20 flex flex-col gap-2"
                >
                  <Settings className="h-6 w-6" />
                  <span>Settings</span>
                </Button>
              </Link>
              <Button
                variant="outline"
                className="w-full h-20 flex flex-col gap-2"
              >
                <TrendingUp className="h-6 w-6" />
                <span>Analytics</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
