"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { ArrowLeft, Edit, Save, X, Loader2 } from "lucide-react";

export default function UserView({ params }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editedUser, setEditedUser] = useState({});
  const router = useRouter();

  const getAuthHeaders = () => {
    const token =
      localStorage.getItem("managerToken") ||
      localStorage.getItem("accessToken") ||
      localStorage.getItem("adminToken");

    return {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };
  };

  const getApiUrl = (endpoint) => {
    const schemaName = localStorage.getItem("schemaName");

    if (!schemaName) {
      console.error("No schema name found in localStorage");
      toast.error("Authentication error: Please log in again");
      return null;
    }

    const subdomain = schemaName.replace(/_/g, "-");
    const baseUrl = `http://${subdomain}.localhost:8000/`;

    return `${baseUrl}api/tenant/${endpoint}`;
  };

  useEffect(() => {
    fetchUser();
  }, [params.id]);

  const fetchUser = async () => {
    try {
      const apiUrl = getApiUrl(`staff/${params.id}/`);
      if (!apiUrl) return;

      const response = await fetch(apiUrl, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        setEditedUser(userData);
      } else {
        console.error("Failed to fetch user:", response.status);
        toast.error(`Failed to fetch user: ${response.status}`);
      }
    } catch (error) {
      console.error("Network error fetching user:", error);
      toast.error("Failed to fetch user");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const apiUrl = getApiUrl(`staff/${params.id}/`);
      if (!apiUrl) return;

      const response = await fetch(apiUrl, {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(editedUser),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        setUser(updatedUser);
        setEditing(false);
        toast.success("User updated successfully");
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error("Failed to update user:", response.status, errorData);
        toast.error(`Failed to update user: ${response.status}`);
      }
    } catch (error) {
      console.error("Network error updating user:", error);
      toast.error("Network error");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedUser(user);
    setEditing(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading user details...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">User not found</p>
          <Button onClick={() => router.back()} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {user.first_name} {user.last_name}
              </h1>
              <p className="text-gray-600">Staff Member Details</p>
            </div>
          </div>
        </div>

        {/* User Details Card */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>User Information</CardTitle>
                <CardDescription>
                  Manage staff member details and permissions
                </CardDescription>
              </div>
              <div className="flex gap-2">
                {editing ? (
                  <>
                    <Button
                      variant="outline"
                      onClick={handleCancel}
                      disabled={saving}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                    <Button onClick={handleSave} disabled={saving}>
                      {saving ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      Save Changes
                    </Button>
                  </>
                ) : (
                  <Button onClick={() => setEditing(true)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="first_name">First Name</Label>
                {editing ? (
                  <Input
                    id="first_name"
                    value={editedUser.first_name || ""}
                    onChange={(e) =>
                      setEditedUser({
                        ...editedUser,
                        first_name: e.target.value,
                      })
                    }
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900">
                    {user.first_name || "Not provided"}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="last_name">Last Name</Label>
                {editing ? (
                  <Input
                    id="last_name"
                    value={editedUser.last_name || ""}
                    onChange={(e) =>
                      setEditedUser({
                        ...editedUser,
                        last_name: e.target.value,
                      })
                    }
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900">
                    {user.last_name || "Not provided"}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="email">Email</Label>
                {editing ? (
                  <Input
                    id="email"
                    type="email"
                    value={editedUser.email || ""}
                    onChange={(e) =>
                      setEditedUser({ ...editedUser, email: e.target.value })
                    }
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900">
                    {user.email || "Not provided"}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="phone_number">Phone Number</Label>
                {editing ? (
                  <Input
                    id="phone_number"
                    value={editedUser.phone_number || ""}
                    onChange={(e) =>
                      setEditedUser({
                        ...editedUser,
                        phone_number: e.target.value,
                      })
                    }
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900">
                    {user.phone_number || "Not provided"}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="username">Username</Label>
                <p className="mt-1 text-sm text-gray-900">{user.username}</p>
                <p className="text-xs text-gray-500">
                  Username cannot be changed
                </p>
              </div>

              <div>
                <Label htmlFor="role">Role</Label>
                {editing ? (
                  <Select
                    value={editedUser.role || ""}
                    onValueChange={(value) =>
                      setEditedUser({ ...editedUser, role: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="waiter">Waiter</SelectItem>
                      <SelectItem value="delivery">Delivery</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="mt-1">
                    <Badge variant="outline" className="capitalize">
                      {user.role}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
