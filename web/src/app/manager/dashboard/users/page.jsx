"use client";

import { useState, useEffect } from "react";
import { DataTable } from "@/components/dataTable";
import { columns } from "./colums";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Plus, Loader2, AlertCircle, Info } from "lucide-react";

export default function UsersPage() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({});
  const [newUser, setNewUser] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    role: "",
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const getAuthHeaders = () => {
    // Try different token storage keys based on user type
    const token =
      localStorage.getItem("managerToken") ||
      localStorage.getItem("accessToken") ||
      localStorage.getItem("adminToken");

    return {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };
  };

  const getApiUrl = (endpoint) => {
    // Get the schema name from localStorage (stored during login)
    const schemaName = localStorage.getItem("schemaName");

    if (!schemaName) {
      console.error("No schema name found in localStorage");
      toast.error("Authentication error: Please log in again");
      return null;
    }

    // Convert schema name to subdomain format (e.g., tahit_restaurant -> tahit-restaurant)
    const subdomain = schemaName.replace(/_/g, "-");

    // Use the tenant-specific subdomain URL
    const baseUrl = `http://${subdomain}.localhost:8000/`;

    // For tenant-specific endpoints, we use the tenant API routes
    return `${baseUrl}api/tenant/${endpoint}`;
  };

  const fetchUsers = async () => {
    try {
      const apiUrl = getApiUrl("staff/");
      if (!apiUrl) return;

      const response = await fetch(apiUrl, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        // Transform the data to match the expected format for the DataTable
        const transformedUsers = (data.results || data).map((user) => ({
          id: user.id,
          name: `${user.first_name} ${user.last_name}`.trim() || user.username,
          email: user.email,
          role: user.role,
          phone_number: user.phone_number,
          username: user.username,
          is_active: user.is_active,
        }));
        setUsers(transformedUsers);
      } else {
        console.error(
          "Failed to fetch users:",
          response.status,
          response.statusText
        );

        // Handle detailed error response from Django
        try {
          const errorData = await response.json();
          displayDjangoErrors(errorData);
        } catch {
          toast.error(`Failed to fetch users: ${response.status}`);
        }
      }
    } catch (error) {
      console.error("Network error fetching users:", error);
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  // Helper function to display Django error messages
  const displayDjangoErrors = (errorData) => {
    // Clear previous field errors
    setFieldErrors({});

    // Handle different Django error response formats
    if (errorData.detail) {
      // Single error message (like authentication errors)
      toast.error(errorData.detail);
    } else if (errorData.non_field_errors) {
      // Non-field errors (general validation errors)
      errorData.non_field_errors.forEach((error) => {
        toast.error(error);
      });
    } else {
      // Field-specific errors
      const errors = {};
      let hasFieldErrors = false;

      Object.keys(errorData).forEach((field) => {
        if (Array.isArray(errorData[field])) {
          errors[field] = errorData[field];
          hasFieldErrors = true;

          // Also show field errors as toasts for immediate visibility
          errorData[field].forEach((error) => {
            toast.error(`${field.replace("_", " ")}: ${error}`);
          });
        } else if (typeof errorData[field] === "string") {
          errors[field] = [errorData[field]];
          hasFieldErrors = true;
          toast.error(`${field.replace("_", " ")}: ${errorData[field]}`);
        }
      });

      if (hasFieldErrors) {
        setFieldErrors(errors);
      } else {
        // Fallback for unexpected error format
        toast.error("An error occurred. Please try again.");
      }
    }
  };

  // Helper function to get field error message
  const getFieldError = (fieldName) => {
    return fieldErrors[fieldName] ? fieldErrors[fieldName][0] : null;
  };

  // Clear field error when user starts typing
  const handleInputChange = (field, value) => {
    setNewUser({ ...newUser, [field]: value });

    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      const newErrors = { ...fieldErrors };
      delete newErrors[field];
      setFieldErrors(newErrors);
    }
  };

  const handleAddUser = async () => {
    if (
      !newUser.first_name ||
      !newUser.last_name ||
      !newUser.email ||
      !newUser.role
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);
    setFieldErrors({}); // Clear previous errors

    try {
      const apiUrl = getApiUrl("staff/");
      if (!apiUrl) return;

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(newUser),
      });

      if (response.ok) {
        const createdUser = await response.json();
        toast.success("Staff member added successfully");

        // Show the generated password to the manager
        if (createdUser.password) {
          const message = `Generated credentials - Username: ${createdUser.username}, Password: ${createdUser.password}`;

          toast.success(message, {
            duration: Infinity, // will not auto-close
            action: {
              label: "Copy",
              onClick: () => {
                navigator.clipboard.writeText(message);
                toast.success("Copied to clipboard");
              },
            },
            description: (
              <a
                href={`mailto:${createdUser.email}?subject=Your Login Credentials&body=Username: ${createdUser.username}%0APassword: ${createdUser.password}`}
                className="text-blue-500 underline mt-2 block"
              >
                Send via Email
              </a>
            ),
          });
        }

        setIsAddUserOpen(false);
        setNewUser({
          first_name: "",
          last_name: "",
          email: "",
          phone_number: "",
          role: "",
        });
        setFieldErrors({}); // Clear errors on success
        fetchUsers(); // Refresh the list
      } else {
        // Handle Django error response
        try {
          const errorData = await response.json();
          console.error("Failed to add user:", response.status, errorData);
          displayDjangoErrors(errorData);
        } catch {
          toast.error(`Failed to add user: ${response.status}`);
        }
      }
    } catch (error) {
      console.error("Network error adding user:", error);
      toast.error("Network error");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="mx-auto py-5 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading staff members...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto py-5">
      <div className="flex">
        <div className="flex-1">
          <div className="text-lg font-bold">Staff Management</div>
          <div className="text-sm text-gray-500">
            Manage the staff members in your restaurant
          </div>
        </div>
        <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Staff Member
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Staff Member</DialogTitle>
              <DialogDescription>
                Create a new staff account. Login credentials will be generated
                automatically.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first_name">First Name *</Label>
                  <Input
                    id="first_name"
                    value={newUser.first_name}
                    onChange={(e) =>
                      handleInputChange("first_name", e.target.value)
                    }
                    placeholder="Enter first name"
                    className={
                      getFieldError("first_name")
                        ? "border-red-500 focus-visible:ring-red-500"
                        : ""
                    }
                  />
                  {getFieldError("first_name") && (
                    <div className="flex items-center mt-1 text-sm text-red-600">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {getFieldError("first_name")}
                    </div>
                  )}
                </div>
                <div>
                  <Label htmlFor="last_name">Last Name *</Label>
                  <Input
                    id="last_name"
                    value={newUser.last_name}
                    onChange={(e) =>
                      handleInputChange("last_name", e.target.value)
                    }
                    placeholder="Enter last name"
                    className={
                      getFieldError("last_name")
                        ? "border-red-500 focus-visible:ring-red-500"
                        : ""
                    }
                  />
                  {getFieldError("last_name") && (
                    <div className="flex items-center mt-1 text-sm text-red-600">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {getFieldError("last_name")}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="Enter email address"
                  className={
                    getFieldError("email")
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {getFieldError("email") && (
                  <div className="flex items-center mt-1 text-sm text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {getFieldError("email")}
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="phone_number">Phone Number</Label>
                <Input
                  id="phone_number"
                  value={newUser.phone_number}
                  onChange={(e) =>
                    handleInputChange("phone_number", e.target.value)
                  }
                  placeholder="Enter phone number starting with country code"
                  className={
                    getFieldError("phone_number")
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                <div className="flex items-center mt-1 text-sm text-blue-600">
                  <Info className="h-4 w-4 mr-1" />
                  Make sure the phone number starts with country code (e.g
                  +265).
                </div>
                {getFieldError("phone_number") && (
                  <div className="flex items-center mt-1 text-sm text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {getFieldError("phone_number")}.
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="role">Role *</Label>
                <Select
                  value={newUser.role}
                  onValueChange={(value) => handleInputChange("role", value)}
                >
                  <SelectTrigger
                    className={
                      getFieldError("role")
                        ? "border-red-500 focus:ring-red-500"
                        : ""
                    }
                  >
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="waiter">Waiter</SelectItem>
                    <SelectItem value="delivery">Delivery</SelectItem>
                  </SelectContent>
                </Select>
                {getFieldError("role") && (
                  <div className="flex items-center mt-1 text-sm text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {getFieldError("role")}
                  </div>
                )}
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> Username and password will be generated
                  automatically. Make sure to save the credentials when they are
                  displayed.
                </p>
              </div>

              <Button
                onClick={handleAddUser}
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Add Staff Member"
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <DataTable columns={columns} data={users} />
    </div>
  );
}
