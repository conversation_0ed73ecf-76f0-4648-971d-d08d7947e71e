"use client";

import DashboardLayout from "@/components/dashboardLayout";
import { FileUser, Home, Settings, Users, ChefHat } from "lucide-react";

const adminMenu = [
  {
    title: "Dashboard",
    url: "/manager/dashboard/home",
    icon: Home,
  },
  {
    title: "Menu Management",
    url: "/manager/dashboard/menu",
    icon: ChefHat,
  },
  {
    title: "Users",
    url: "/manager/dashboard/users",
    icon: Users,
  },
  {
    title: "Restaurants",
    url: "/manager/dashboard/restaurants",
    icon: FileUser,
  },
  {
    title: "Settings",
    url: "/manager/dashboard/settings",
    icon: Settings,
  },
];

export default function AdminLayout({ children }) {
  return (
    <DashboardLayout sidebarItems={adminMenu} role="Manager">
      {children}
    </DashboardLayout>
  );
}
