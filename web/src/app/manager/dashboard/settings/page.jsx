"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Settings,
  Store,
  CreditCard,
  Clock,
  MapPin,
  Phone,
  Mail,
  Save,
  Loader2,
  Upload,
} from "lucide-react";
import Image from "next/image";

export default function SettingsPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [restaurantInfo, setRestaurantInfo] = useState({});
  const [config, setConfig] = useState({});
  const [editedConfig, setEditedConfig] = useState({});
  const [theme, setTheme] = useState({});
  const [logoPreview, setLogoPreview] = useState(null);
  const [bannerPreview, setBannerPreview] = useState(null);

  const getAuthHeaders = () => {
    const token =
      localStorage.getItem("managerToken") ||
      localStorage.getItem("accessToken") ||
      localStorage.getItem("adminToken");

    return {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };
  };

  const getApiUrl = (endpoint) => {
    const schemaName = localStorage.getItem("schemaName");

    if (!schemaName) {
      console.error("No schema name found in localStorage");
      toast.error("Authentication error: Please log in again");
      return null;
    }

    const subdomain = schemaName.replace(/_/g, "-");
    const baseUrl = `http://${subdomain}.localhost:8000/`;

    return `${baseUrl}api/tenant/${endpoint}`;
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      // Fetch restaurant configuration
      const configUrl = getApiUrl("config/");
      if (!configUrl) return;

      const configResponse = await fetch(configUrl, {
        headers: getAuthHeaders(),
      });

      if (configResponse.ok) {
        const configData = await configResponse.json();
        const configuration = configData.results?.[0] || configData[0] || {};
        setConfig(configuration);
        setEditedConfig(configuration);
      }

      // Try to fetch restaurant details (logo, banner, etc.)
      try {
        const restaurantUrl = getApiUrl("restaurant/");
        if (restaurantUrl) {
          const restaurantResponse = await fetch(restaurantUrl, {
            headers: getAuthHeaders(),
          });
          if (restaurantResponse.ok) {
            const restaurantData = await restaurantResponse.json();
            setRestaurantInfo((prev) => ({
              ...prev,
              ...restaurantData,
            }));
          }
        }
      } catch (error) {
        console.log("Restaurant details endpoint not available");
      }

      // Try to fetch theme configuration
      try {
        const themeUrl = getApiUrl("theme/");
        if (themeUrl) {
          const themeResponse = await fetch(themeUrl, {
            headers: getAuthHeaders(),
          });
          if (themeResponse.ok) {
            const themeData = await themeResponse.json();
            setTheme(themeData);
          }
        }
      } catch (error) {
        console.log("Theme endpoint not available");
        // Set mock theme data to demonstrate functionality
        setTheme({
          primary_color: "#FF6600",
          secondary_color: "#FFFFFF",
          accent_color: "#FFD700",
          font_family: "Roboto",
          rounded_corners: true,
          dark_mode: false,
        });
      }

      // Get restaurant info from localStorage (stored during login)
      const restaurantName = localStorage.getItem("restaurantName");
      const schemaName = localStorage.getItem("schemaName");

      setRestaurantInfo((prev) => ({
        restaurant_name: restaurantName || "Restaurant",
        schema_name: schemaName,
        ...prev,
      }));
    } catch (error) {
      console.error("Network error fetching settings:", error);
      toast.error("Failed to fetch settings");
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e, type) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Invalid file type. Please upload an image file.");
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File too large. Please upload an image smaller than 5MB.");
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      if (type === "logo") {
        setLogoPreview(e.target.result);
      } else if (type === "banner") {
        setBannerPreview(e.target.result);
      }
    };
    reader.readAsDataURL(file);

    // Upload file
    handleFileUpload(file, type);
  };

  const handleFileUpload = async (file, type) => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append(type, file);

      // Try to upload to restaurant endpoint
      const uploadUrl = getApiUrl("restaurant/");
      if (uploadUrl) {
        const response = await fetch(uploadUrl, {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${
              localStorage.getItem("managerToken") ||
              localStorage.getItem("accessToken") ||
              localStorage.getItem("adminToken")
            }`,
          },
          body: formData,
        });

        if (response.ok) {
          const updatedData = await response.json();
          setRestaurantInfo((prev) => ({
            ...prev,
            ...updatedData,
          }));
          toast.success(
            `${type === "logo" ? "Logo" : "Banner"} updated successfully`
          );
        } else {
          toast.error(
            `Failed to upload ${type}. Endpoint may not be available.`
          );
        }
      } else {
        toast.error("Upload endpoint not available");
      }
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);
      toast.error(`Failed to upload ${type}`);
    } finally {
      setUploading(false);
    }
  };

  const handleSaveConfig = async () => {
    setSaving(true);
    try {
      const configUrl = getApiUrl("config/");
      if (!configUrl) return;

      const method = config.id ? "PUT" : "POST";
      const url = config.id ? `${configUrl}${config.id}/` : configUrl;

      const response = await fetch(url, {
        method,
        headers: getAuthHeaders(),
        body: JSON.stringify(editedConfig),
      });

      if (response.ok) {
        const updatedConfig = await response.json();
        setConfig(updatedConfig);
        toast.success("Configuration updated successfully");
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error(
          "Failed to update configuration:",
          response.status,
          errorData
        );
        toast.error(`Failed to update configuration: ${response.status}`);
      }
    } catch (error) {
      console.error("Network error updating configuration:", error);
      toast.error("Network error");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Restaurant Settings
          </h1>
          <p className="text-gray-600">
            Manage your restaurant configuration and preferences
          </p>
        </div>

        <Tabs defaultValue="restaurant" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="restaurant">Restaurant Info</TabsTrigger>
            <TabsTrigger value="operations">Operations</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
          </TabsList>

          {/* Restaurant Information Tab */}
          <TabsContent value="restaurant" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Store className="h-5 w-5" />
                  <CardTitle>Restaurant Information</CardTitle>
                </div>
                <CardDescription>
                  Basic information about your restaurant
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Restaurant Logo */}
                  <div className="flex items-center gap-6">
                    <div className="relative">
                      <div className="w-24 h-24 rounded-full border-2 border-gray-300 overflow-hidden bg-gray-100 flex items-center justify-center">
                        {logoPreview || restaurantInfo.logo ? (
                          <img
                            src={logoPreview || restaurantInfo.logo}
                            alt="Restaurant Logo"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Store className="h-8 w-8 text-gray-400" />
                        )}
                      </div>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleFileChange(e, "logo")}
                        className="hidden"
                        id="logo-upload"
                        disabled={uploading}
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0"
                        onClick={() =>
                          document.getElementById("logo-upload")?.click()
                        }
                        disabled={uploading}
                      >
                        {uploading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Upload className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">
                        {restaurantInfo.restaurant_name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        Schema: {restaurantInfo.schema_name}
                      </p>
                      <Badge variant="outline" className="mt-1">
                        Active
                      </Badge>
                    </div>
                  </div>

                  {/* Restaurant Banner */}
                  <div>
                    <Label htmlFor="banner">Restaurant Banner</Label>
                    <div className="mt-2">
                      <div className="relative w-full h-32 border-2 border-dashed border-gray-300 rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center">
                        {bannerPreview || restaurantInfo.banner ? (
                          <img
                            src={bannerPreview || restaurantInfo.banner}
                            alt="Restaurant Banner"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="text-center">
                            <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-500">
                              Click to upload banner
                            </p>
                          </div>
                        )}
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileChange(e, "banner")}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                          disabled={uploading}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Recommended size: 1200x300px. Max file size: 5MB
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="restaurant_name">Restaurant Name</Label>
                      <Input
                        id="restaurant_name"
                        value={restaurantInfo.restaurant_name || ""}
                        disabled
                        className="bg-gray-50"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Contact support to change restaurant name
                      </p>
                    </div>
                    <div>
                      <Label htmlFor="schema_name">Schema Name</Label>
                      <Input
                        id="schema_name"
                        value={restaurantInfo.schema_name || ""}
                        disabled
                        className="bg-gray-50"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        System identifier (cannot be changed)
                      </p>
                    </div>
                  </div>

                  {/* Restaurant Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="restaurant_phone">Phone Number</Label>
                      <Input
                        id="restaurant_phone"
                        value={restaurantInfo.restaurant_phone || ""}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                    <div>
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        value={restaurantInfo.address || ""}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                  </div>

                  {restaurantInfo.description && (
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={restaurantInfo.description || ""}
                        disabled
                        className="bg-gray-50"
                        rows={3}
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Theme Configuration Card */}
            {Object.keys(theme).length > 0 && (
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    <CardTitle>Theme Configuration</CardTitle>
                  </div>
                  <CardDescription>
                    Visual appearance and branding settings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <Label>Primary Color</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <div
                          className="w-8 h-8 rounded border border-gray-300"
                          style={{ backgroundColor: theme.primary_color }}
                        />
                        <span className="text-sm font-mono">
                          {theme.primary_color}
                        </span>
                      </div>
                    </div>
                    <div>
                      <Label>Secondary Color</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <div
                          className="w-8 h-8 rounded border border-gray-300"
                          style={{ backgroundColor: theme.secondary_color }}
                        />
                        <span className="text-sm font-mono">
                          {theme.secondary_color}
                        </span>
                      </div>
                    </div>
                    <div>
                      <Label>Accent Color</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <div
                          className="w-8 h-8 rounded border border-gray-300"
                          style={{ backgroundColor: theme.accent_color }}
                        />
                        <span className="text-sm font-mono">
                          {theme.accent_color}
                        </span>
                      </div>
                    </div>
                    <div>
                      <Label>Font Family</Label>
                      <p
                        className="text-sm mt-1"
                        style={{ fontFamily: theme.font_family }}
                      >
                        {theme.font_family}
                      </p>
                    </div>
                    <div>
                      <Label>Rounded Corners</Label>
                      <Badge
                        variant={
                          theme.rounded_corners ? "default" : "secondary"
                        }
                        className="mt-1"
                      >
                        {theme.rounded_corners ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                    <div>
                      <Label>Dark Mode</Label>
                      <Badge
                        variant={theme.dark_mode ? "default" : "secondary"}
                        className="mt-1"
                      >
                        {theme.dark_mode ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Operations Tab */}
          <TabsContent value="operations" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      <CardTitle>Operations Configuration</CardTitle>
                    </div>
                    <CardDescription>
                      Configure delivery, timing, and operational settings
                    </CardDescription>
                  </div>
                  <Button onClick={handleSaveConfig} disabled={saving}>
                    {saving ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Save Changes
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Operating Hours */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="opening_time">Opening Time</Label>
                      <Input
                        id="opening_time"
                        type="time"
                        value={editedConfig.opening_time || ""}
                        onChange={(e) =>
                          setEditedConfig({
                            ...editedConfig,
                            opening_time: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="closing_time">Closing Time</Label>
                      <Input
                        id="closing_time"
                        type="time"
                        value={editedConfig.closing_time || ""}
                        onChange={(e) =>
                          setEditedConfig({
                            ...editedConfig,
                            closing_time: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>

                  {/* Delivery Settings */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="delivery_radius">
                        Delivery Radius (km)
                      </Label>
                      <Input
                        id="delivery_radius"
                        type="number"
                        step="0.1"
                        value={editedConfig.delivery_radius_km || ""}
                        onChange={(e) =>
                          setEditedConfig({
                            ...editedConfig,
                            delivery_radius_km: parseFloat(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="delivery_fee">Delivery Fee (MWK)</Label>
                      <Input
                        id="delivery_fee"
                        type="number"
                        step="0.01"
                        value={editedConfig.delivery_fee || ""}
                        onChange={(e) =>
                          setEditedConfig({
                            ...editedConfig,
                            delivery_fee: parseFloat(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="minimum_order">
                      Minimum Order Amount (MWK)
                    </Label>
                    <Input
                      id="minimum_order"
                      type="number"
                      step="0.01"
                      value={editedConfig.minimum_order_amount || ""}
                      onChange={(e) =>
                        setEditedConfig({
                          ...editedConfig,
                          minimum_order_amount: parseFloat(e.target.value) || 0,
                        })
                      }
                    />
                  </div>

                  {/* Notification Settings */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Notifications</h3>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="email_notifications">
                          Email Confirmations
                        </Label>
                        <p className="text-sm text-gray-500">
                          Send order confirmations via email
                        </p>
                      </div>
                      <Switch
                        id="email_notifications"
                        checked={editedConfig.order_confirmation_email || false}
                        onCheckedChange={(checked) =>
                          setEditedConfig({
                            ...editedConfig,
                            order_confirmation_email: checked,
                          })
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="sms_notifications">
                          SMS Confirmations
                        </Label>
                        <p className="text-sm text-gray-500">
                          Send order confirmations via SMS
                        </p>
                      </div>
                      <Switch
                        id="sms_notifications"
                        checked={editedConfig.order_confirmation_sms || false}
                        onCheckedChange={(checked) =>
                          setEditedConfig({
                            ...editedConfig,
                            order_confirmation_sms: checked,
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payments Tab */}
          <TabsContent value="payments" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      <CardTitle>Payment Configuration</CardTitle>
                    </div>
                    <CardDescription>
                      Configure accepted payment methods
                    </CardDescription>
                  </div>
                  <Button onClick={handleSaveConfig} disabled={saving}>
                    {saving ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Save Changes
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="online_payment">Online Payment</Label>
                      <p className="text-sm text-gray-500">
                        Accept online payments (cards, mobile money)
                      </p>
                    </div>
                    <Switch
                      id="online_payment"
                      checked={editedConfig.enable_online_payment || false}
                      onCheckedChange={(checked) =>
                        setEditedConfig({
                          ...editedConfig,
                          enable_online_payment: checked,
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="cash_delivery">Cash on Delivery</Label>
                      <p className="text-sm text-gray-500">
                        Accept cash payments on delivery
                      </p>
                    </div>
                    <Switch
                      id="cash_delivery"
                      checked={editedConfig.enable_cash_on_delivery || false}
                      onCheckedChange={(checked) =>
                        setEditedConfig({
                          ...editedConfig,
                          enable_cash_on_delivery: checked,
                        })
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
