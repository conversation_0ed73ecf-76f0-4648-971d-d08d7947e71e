"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Star,
  MapPin,
  Clock,
  Search,
  Filter,
  Navigation,
  ChefHat,
} from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import CustomerHeader from "@/components/customer-header";

export default function RestaurantsPage() {
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("rating");
  const [filterBy, setFilterBy] = useState("all");
  const [showNearby, setShowNearby] = useState(
    searchParams?.get("nearby") === "true"
  );
  const [userLocation, setUserLocation] = useState(null);
  const [restaurants, setRestaurants] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch restaurants from API
  useEffect(() => {
    const fetchRestaurants = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          "http://localhost:8000/api/public/restaurants/"
        );

        if (response.ok) {
          const data = await response.json();
          const formattedRestaurants = (data.results || data).map(
            (restaurant) => ({
              id: restaurant.id,
              name: restaurant.restaurant_name || restaurant.name,
              cuisine: restaurant.cuisine || "Various",
              rating: restaurant.rating || 4.0 + Math.random() * 1.0, // Random rating between 4.0-5.0
              reviews:
                restaurant.review_count || Math.floor(Math.random() * 500) + 50,
              distance: Math.round((Math.random() * 5 + 0.5) * 10) / 10, // Random distance 0.5-5.5km
              coordinates: restaurant.coordinates || [-15.7861, 35.0058], // Default to Blantyre
              image:
                restaurant.banner ||
                restaurant.logo ||
                "/placeholder.svg?height=200&width=300",
              deliveryTime: `${Math.floor(Math.random() * 20) + 20}-${
                Math.floor(Math.random() * 20) + 35
              } min`,
              priceRange: restaurant.price_range || "$$",
              isOpen:
                restaurant.is_open !== undefined ? restaurant.is_open : true,
              description:
                restaurant.description || "Delicious food with great service",
            })
          );
          setRestaurants(formattedRestaurants);
        } else {
          // Fallback to mock data if API fails
          setRestaurants([
            {
              id: 1,
              name: "Blantyre Bites",
              cuisine: "Malawian",
              rating: 4.8,
              reviews: 324,
              distance: 0.5,
              coordinates: [-15.7861, 35.0058],
              image: "/placeholder.svg?height=200&width=300",
              deliveryTime: "25-35 min",
              priceRange: "$$",
              isOpen: true,
              description: "Delicious Malawian dishes with a modern touch",
            },
            {
              id: 2,
              name: "Lilongwe Pasta House",
              cuisine: "Italian",
              rating: 4.6,
              reviews: 189,
              distance: 1.2,
              coordinates: [-13.9639, 33.7741],
              image: "/placeholder.svg?height=200&width=300",
              deliveryTime: "30-40 min",
              priceRange: "$$$",
              isOpen: true,
              description: "Authentic Italian pasta and pizza",
            },
            {
              id: 3,
              name: "Sushi Spot Malawi",
              cuisine: "Japanese",
              rating: 4.9,
              reviews: 456,
              distance: 0.8,
              coordinates: [-15.3852, 35.3375],
              image: "/placeholder.svg?height=200&width=300",
              deliveryTime: "20-30 min",
              priceRange: "$$$$",
              isOpen: true,
              description: "Fresh sushi and traditional Japanese dishes",
            },
          ]);
        }
      } catch (error) {
        console.error("Failed to fetch restaurants:", error);
        // Fallback to mock data on error
        setRestaurants([
          {
            id: 1,
            name: "Blantyre Bites",
            cuisine: "Malawian",
            rating: 4.8,
            reviews: 324,
            distance: 0.5,
            coordinates: [-15.7861, 35.0058],
            image: "/placeholder.svg?height=200&width=300",
            deliveryTime: "25-35 min",
            priceRange: "$$",
            isOpen: true,
            description: "Delicious Malawian dishes with a modern touch",
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchRestaurants();
  }, []);
  //   {
  //     id: 6,
  //     name: "Lake Shore BBQ",
  //     cuisine: "BBQ",
  //     rating: 4.5,
  //     reviews: 342,
  //     distance: 3.2,
  //     coordinates: [-14.4781, 34.9131], // Near Lake Malawi
  //     image: "/placeholder.svg?height=200&width=300",
  //     deliveryTime: "35-45 min",
  //     priceRange: "$$$",
  //     isOpen: true,
  //     description: "Slow-cooked BBQ with locally sourced meats",
  //   },
  // ];

  useEffect(() => {
    if (showNearby && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation([
            position.coords.latitude,
            position.coords.longitude,
          ]);
        },
        (error) => {
          console.error("Error getting location:", error);
          setShowNearby(false);
        }
      );
    }
  }, [showNearby]);

  const filteredRestaurants = restaurants
    .filter((restaurant) => {
      const matchesSearch =
        restaurant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        restaurant.cuisine.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter =
        filterBy === "all" ||
        restaurant.cuisine.toLowerCase() === filterBy.toLowerCase();
      const matchesNearby = !showNearby || restaurant.distance <= 2;
      return matchesSearch && matchesFilter && matchesNearby;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "rating":
          return b.rating - a.rating;
        case "distance":
          return a.distance - b.distance;
        case "reviews":
          return b.reviews - a.reviews;
        case "name":
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

  const cuisineTypes = ["all", ...new Set(restaurants.map((r) => r.cuisine))];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <CustomerHeader />
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading restaurants...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <CustomerHeader />

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {showNearby ? "Restaurants Near You" : "All Restaurants"}
          </h1>
          <p className="text-gray-600">
            {showNearby
              ? "Discover great food options in your area"
              : `Found ${filteredRestaurants.length} restaurants`}
          </p>
        </div>

        {/* Filters and Search */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search restaurants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={filterBy} onValueChange={setFilterBy}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by cuisine" />
                </SelectTrigger>
                <SelectContent>
                  {cuisineTypes.map((cuisine) => (
                    <SelectItem key={cuisine} value={cuisine}>
                      {cuisine === "all" ? "All Cuisines" : cuisine}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rating">Highest Rated</SelectItem>
                  <SelectItem value="distance">Nearest</SelectItem>
                  <SelectItem value="reviews">Most Reviews</SelectItem>
                  <SelectItem value="name">Name A-Z</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant={showNearby ? "default" : "outline"}
                onClick={() => setShowNearby(!showNearby)}
                className="flex items-center gap-2"
              >
                <Navigation className="h-4 w-4" />
                {showNearby ? "Showing Nearby" : "Show Nearby"}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Restaurant Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRestaurants.map((restaurant) => (
            <Card
              key={restaurant.id}
              className="overflow-hidden hover:shadow-lg transition-shadow"
            >
              <div className="relative">
                <img
                  src={restaurant.image || "/placeholder.svg"}
                  alt={restaurant.name}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 left-3 flex gap-2">
                  <Badge
                    className={
                      restaurant.isOpen ? "bg-green-600" : "bg-red-600"
                    }
                  >
                    {restaurant.isOpen ? "Open" : "Closed"}
                  </Badge>
                  <Badge variant="secondary">{restaurant.priceRange}</Badge>
                </div>
                <div className="absolute top-3 right-3">
                  <div className="bg-white rounded-full px-2 py-1 flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">
                      {restaurant.rating}
                    </span>
                  </div>
                </div>
              </div>

              <CardContent className="p-6">
                <div className="mb-3">
                  <h3 className="text-xl font-semibold text-gray-900 mb-1">
                    {restaurant.name}
                  </h3>
                  <p className="text-gray-600 text-sm mb-2">
                    {restaurant.description}
                  </p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span className="font-medium text-orange-600">
                      {restaurant.cuisine}
                    </span>
                    <span>•</span>
                    <span>{restaurant.reviews} reviews</span>
                  </div>
                </div>

                <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    {restaurant.distance} km away
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {restaurant.deliveryTime}
                  </div>
                </div>

                <Link href={`/customer/restaurants/${restaurant.id}`}>
                  <Button className="w-full">View Restaurant</Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredRestaurants.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No restaurants found
            </h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your search or filters
            </p>
            <Button
              onClick={() => {
                setSearchTerm("");
                setFilterBy("all");
                setShowNearby(false);
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
