"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Star,
  MapPin,
  Search,
  Clock,
  TrendingUp,
  Users,
  ChefHat,
  ShoppingCart,
  Heart,
  User,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function CustomerDashboard() {
  const [searchQuery, setSearchQuery] = useState("");
  const [user, setUser] = useState({
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
  });
  const [recentOrders, setRecentOrders] = useState([]);
  const [recommendedForYou, setRecommendedForYou] = useState([]);
  const [loading, setLoading] = useState(true);

  // Get auth headers for API calls
  const getAuthHeaders = () => {
    const token =
      localStorage.getItem("customerToken") ||
      localStorage.getItem("accessToken");
    return {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  };

  // Fetch user data and dashboard content
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch user profile
        try {
          const userResponse = await fetch(
            "http://localhost:8000/api/public/customer/profile/",
            {
              headers: getAuthHeaders(),
            }
          );
          if (userResponse.ok) {
            const userData = await userResponse.json();
            setUser({
              name:
                `${userData.first_name || ""} ${
                  userData.last_name || ""
                }`.trim() ||
                userData.username ||
                "Customer",
              avatar: userData.avatar || "/placeholder.svg?height=40&width=40",
              email: userData.email,
            });
          }
        } catch (error) {
          console.log("User profile not available, using defaults");
        }

        // Fetch recent orders
        try {
          const ordersResponse = await fetch(
            "http://localhost:8000/api/public/customer/orders/recent/",
            {
              headers: getAuthHeaders(),
            }
          );
          if (ordersResponse.ok) {
            const ordersData = await ordersResponse.json();
            const formattedOrders = (ordersData.results || ordersData)
              .slice(0, 3)
              .map((order) => ({
                id: order.id,
                restaurant: order.restaurant?.name || "Restaurant",
                items: order.items?.map((item) => item.name) || ["Order items"],
                total: `MK ${order.total_amount || "0"}`,
                date:
                  new Date(order.created_at).toLocaleDateString() || "Recently",
                status: order.status || "Delivered",
                image:
                  order.restaurant?.logo ||
                  "/placeholder.svg?height=60&width=60",
              }));
            setRecentOrders(formattedOrders);
          } else {
            // Fallback to mock data
            setRecentOrders([
              {
                id: 1,
                restaurant: "Mzuzu Grill",
                items: ["Grilled Chicken", "Rice & Beans"],
                total: "MK 8,500",
                date: "2 days ago",
                status: "Delivered",
                image: "/placeholder.svg?height=60&width=60",
              },
              {
                id: 2,
                restaurant: "Lake Malawi Fish Spot",
                items: ["Grilled Fish", "Nsima"],
                total: "MK 6,200",
                date: "5 days ago",
                status: "Delivered",
                image: "/placeholder.svg?height=60&width=60",
              },
            ]);
          }
        } catch (error) {
          console.log("Orders not available, using mock data");
          setRecentOrders([
            {
              id: 1,
              restaurant: "Mzuzu Grill",
              items: ["Grilled Chicken", "Rice & Beans"],
              total: "MK 8,500",
              date: "2 days ago",
              status: "Delivered",
              image: "/placeholder.svg?height=60&width=60",
            },
          ]);
        }

        // Fetch recommended restaurants
        try {
          const recommendationsResponse = await fetch(
            "http://localhost:8000/api/public/restaurants/",
            {
              headers: getAuthHeaders(),
            }
          );
          if (recommendationsResponse.ok) {
            const restaurantsData = await recommendationsResponse.json();
            const formatted = (restaurantsData.results || restaurantsData)
              .slice(0, 3)
              .map((restaurant) => ({
                id: restaurant.id,
                name: restaurant.restaurant_name || restaurant.name,
                cuisine: restaurant.cuisine || "Various",
                rating: restaurant.rating || 4.5,
                reviews:
                  restaurant.review_count ||
                  Math.floor(Math.random() * 500) + 50,
                distance: "1.2 km",
                image:
                  restaurant.banner ||
                  restaurant.logo ||
                  "/placeholder.svg?height=200&width=300",
                deliveryTime: "25-35 min",
                promo: Math.random() > 0.5 ? "20% off" : "Free delivery",
              }));
            setRecommendedForYou(formatted);
          } else {
            // Fallback to mock data
            setRecommendedForYou([
              {
                id: 1,
                name: "Mzuzu Grill",
                cuisine: "Malawian",
                rating: 4.8,
                reviews: 324,
                distance: "0.5 km",
                image: "/placeholder.svg?height=200&width=300",
                deliveryTime: "25-35 min",
                promo: "20% off",
              },
              {
                id: 2,
                name: "Nsima & More",
                cuisine: "Traditional Malawian",
                rating: 4.6,
                reviews: 189,
                distance: "1.2 km",
                image: "/placeholder.svg?height=200&width=300",
                deliveryTime: "30-40 min",
                promo: "Free delivery",
              },
            ]);
          }
        } catch (error) {
          console.log("Recommendations not available, using mock data");
          setRecommendedForYou([
            {
              id: 1,
              name: "Mzuzu Grill",
              cuisine: "Malawian",
              rating: 4.8,
              reviews: 324,
              distance: "0.5 km",
              image: "/placeholder.svg?height=200&width=300",
              deliveryTime: "25-35 min",
              promo: "20% off",
            },
          ]);
        }
      } catch (error) {
        console.error("Failed to fetch dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const quickCategories = [
    { name: "Fast Food", icon: "🍔", count: 45 },
    { name: "Traditional", icon: "🍽️", count: 32 },
    { name: "Seafood", icon: "🐟", count: 18 },
    { name: "Vegetarian", icon: "🥗", count: 28 },
    { name: "Desserts", icon: "🍰", count: 12 },
    { name: "Beverages", icon: "🥤", count: 15 },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Image width={40} height={40} src={"/logo1.png"} alt="EasyEats" />
              <span className="text-xl font-bold text-gray-900">EasyEats</span>
            </div>
            <nav className="hidden md:flex items-center gap-6">
              <Link
                href="/customer/restaurants"
                className="text-gray-600 hover:text-orange-600 font-medium"
              >
                Browse
              </Link>
              <Link
                href="/customer/history"
                className="text-gray-600 hover:text-orange-600 font-medium"
              >
                Orders
              </Link>
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-full">
                  <Image
                    src={user.avatar}
                    width={24}
                    height={24}
                    className="rounded-full"
                    alt="User"
                  />
                  <span className="text-sm font-medium">{user.name}</span>
                </div>
                <Link
                  className="border border-2 border-red-600 rounded-md bg-orange-600 hover:bg-orange-400 px-3 py-1 text-white"
                  href={"/auth/logout"}
                >
                  Logout
                </Link>
              </div>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Good afternoon, {user.name.split(" ")[0]}! 👋
          </h1>
          <p className="text-gray-600">What would you like to eat today?</p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Search for restaurants, cuisines, or dishes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 pr-4 py-4 text-lg rounded-xl border-gray-200 focus:border-orange-400"
            />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Link href="/customer/restaurants">
            <Card className="hover:shadow-lg transition-all cursor-pointer border-orange-100 hover:border-orange-300">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <ChefHat className="h-6 w-6 text-orange-600" />
                </div>
                <h4 className="font-semibold text-gray-900 text-sm">
                  All Restaurants
                </h4>
              </CardContent>
            </Card>
          </Link>
          <Link href="/customer/restaurants?nearby=true">
            <Card className="hover:shadow-lg transition-all cursor-pointer border-blue-100 hover:border-blue-300">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <MapPin className="h-6 w-6 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-900 text-sm">Nearby</h4>
              </CardContent>
            </Card>
          </Link>
          <Link href="/customer/favorites">
            <Card className="hover:shadow-lg transition-all cursor-pointer border-red-100 hover:border-red-300">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Heart className="h-6 w-6 text-red-600" />
                </div>
                <h4 className="font-semibold text-gray-900 text-sm">
                  Favorites
                </h4>
              </CardContent>
            </Card>
          </Link>
          <Link href="/customer/history">
            <Card className="hover:shadow-lg transition-all cursor-pointer border-green-100 hover:border-green-300">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Clock className="h-6 w-6 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-900 text-sm">
                  Recent Orders
                </h4>
              </CardContent>
            </Card>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Categories */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Explore Categories
              </h3>

              {/* Scrollable container */}
              <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-gray-400 scrollbar-track-gray-200">
                <div className="flex gap-4 min-w-full md:grid md:grid-cols-6">
                  {quickCategories.map((category, index) => (
                    <Card
                      key={index}
                      className="min-w-[140px] max-w-[160px] hover:shadow-lg transition-shadow cursor-pointer"
                    >
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl mb-2">{category.icon}</div>
                        <h4 className="font-medium text-gray-900 text-sm mb-1">
                          {category.name}
                        </h4>
                        <p className="text-xs text-gray-500">
                          {category.count}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>

            {/* Recommended for You */}
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900">
                  Recommended for You
                </h3>
                <Link href="/customer/restaurants">
                  <Button variant="outline">View All</Button>
                </Link>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {recommendedForYou.map((restaurant) => (
                  <Card
                    key={restaurant.id}
                    className="overflow-hidden hover:shadow-xl transition-all"
                  >
                    <div className="relative">
                      <img
                        src={restaurant.image || "/placeholder.svg"}
                        alt={restaurant.name}
                        className="w-full h-40 object-cover"
                      />
                      {restaurant.promo && (
                        <Badge className="absolute top-3 left-3 bg-orange-600">
                          {restaurant.promo}
                        </Badge>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm border-none hover:bg-white"
                      >
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="text-lg font-semibold text-gray-900">
                          {restaurant.name}
                        </h4>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="text-sm font-medium">
                            {restaurant.rating}
                          </span>
                        </div>
                      </div>
                      <p className="text-gray-600 mb-3">{restaurant.cuisine}</p>
                      <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {restaurant.distance}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {restaurant.deliveryTime}
                        </div>
                      </div>
                      <Link href={`customer/restaurants/${restaurant.id}`}>
                        <Button className="w-full">Order Now</Button>
                      </Link>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Orders */}
            <Card>
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-lg font-semibold text-gray-900">
                    Recent Orders
                  </h4>
                  <Link href="/customer/history">
                    <Button variant="ghost" size="sm">
                      View All
                    </Button>
                  </Link>
                </div>
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div
                      key={order.id}
                      className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                    >
                      <img
                        src={order.image}
                        alt={order.restaurant}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <h5 className="font-medium text-gray-900 text-sm">
                          {order.restaurant}
                        </h5>
                        <p className="text-xs text-gray-500 truncate">
                          {order.items.join(", ")}
                        </p>
                        <p className="text-xs text-gray-400">{order.date}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-sm">{order.total}</p>
                        <Badge variant="secondary" className="text-xs">
                          {order.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
                <Link href="/customer/history">
                  <Button variant="outline" className="w-full mt-4">
                    View Order History
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Promotions */}
            <Card className="bg-gradient-to-r from-orange-500 to-red-500 text-white">
              <CardContent className="p-6">
                <h4 className="text-lg font-semibold mb-2">Special Offer!</h4>
                <p className="text-sm mb-4 text-orange-100">
                  Get 25% off your next order from any restaurant. Valid until
                  tomorrow!
                </p>
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-white text-orange-600 hover:bg-orange-50"
                >
                  Claim Offer
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
