"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useEffect, useState } from "react"

export default function RequestViewPage(){
    const [domain, setDomain] = useState("");
    const [editing, setEditing] = useState(false);

    //sample request. Real request ill be fetched from the backend
    const request = {
        //manager details
        managerFullName: "Samuel <PERSON>",
        managerEmail: "<EMAIL>",
        managerPhone: "099999999",
        //restaurant details
        restaurantName: "Pulp Fiction",
        restaurantPhone: "+265312375678",
        restaurantEmail: "<EMAIL>",
        restaurantGeneralLocation: "Some Area",
        restaurantLocation: "someCoordinates",
        //
        domain: "",
    }

    const handleAssignDomian = () =>{
        setEditing(true);
        setDomain(prompt("Assigning Domain"));
    }

    useEffect(()=> {
        setDomain(request.domain)
    },[])

    return (
        <div className="max-w-xl border border-gray-200 rounded-md p-2 md:p-5 mx-auto my-2 shadow-md">
            <div className="text-lg font-bold text-gray-700 border-b border-gray-200 text-center p-3 mb-2">
                Restaurant Registration Request
            </div>

            {/**Manager Details */}
            <div>
                <table className="w-full">
                    <tbody>
                        <tr>
                            <td colSpan={2}><h3 className="py-3 text-center text-md text-gray-600">Manager Details</h3></td>
                        </tr>

                        <tr>
                            <td className="py-3 text-gray-800 font-semibold">
                                Name :
                            </td>
                            <td>
                                {request.managerFullName}
                            </td>
                        </tr>
                        <tr>
                            <td className="py-3 text-gray-800 font-semibold">
                                Email :
                            </td>
                            <td>
                                {request.managerEmail}
                            </td>
                        </tr>
                       <tr>
                            <td className="pt-3 pb-7 text-gray-800 font-semibold">
                                Phone :
                            </td>
                            <td className="pb-7 pt-3">
                                {request.managerPhone}
                            </td>
                        </tr>

                        {/**Restaurant Details */}
                        <tr>
                            <td colSpan={2}><h3 className="py-3 border-t border-gray-200 text-center text-md text-gray-600">Restaurant Details</h3></td>
                        </tr>

                        <tr>
                            <td className="py-3 text-gray-800 font-semibold">
                                Name :
                            </td>
                            <td>
                                {request.restaurantName}
                            </td>
                        </tr>
                        <tr>
                            <td className="py-3 text-gray-800 font-semibold">
                                Email :
                            </td>
                            <td>
                                {request.restaurantEmail}
                            </td>
                        </tr>
                       <tr>
                            <td className="py-3 text-gray-800 font-semibold">
                                Phone :
                            </td>
                            <td>
                                {request.restaurantPhone}
                            </td>
                        </tr>
                        <tr>
                            <td className="text-gray-800 py-3 font-semibold">
                                General Location :
                            </td>
                            <td>
                                {request.restaurantGeneralLocation}
                            </td>
                        </tr>
                        <tr>
                            <td className="text-gray-800 pt-3 pb-7 font-semibold">
                                Precise Location :
                            </td>
                            <td className="pb-7 pt-3">
                                {request.restaurantLocation}
                            </td>
                        </tr>

                        {/**Domain Details */}
                        <tr>
                            <td colSpan={2}><h3 className="py-3 border-t border-gray-200 text-center text-md text-gray-600">Domain</h3></td>
                        </tr>
                        <tr>
                            <td className="text-gray-800 py-3 font-semibold">
                                Domain Name :
                            </td>
                            <td>
                                {domain ? domain : "N/A"}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div className="mt-5 w-full">
                <h3 className="pb-2 text-center text-md text-gray-600">Actions</h3>
                <div className="flex">
                    <div className="flex-1"></div>
                    <Button size={"sm"} className="mx-1" onClick={handleAssignDomian}>Assign Domain</Button>
                    {editing && <Button size={"sm"} onClick={() => alert("Changes Saved Successfully!")}>Save</Button>}
                </div>
            </div>
        </div>
    )
}