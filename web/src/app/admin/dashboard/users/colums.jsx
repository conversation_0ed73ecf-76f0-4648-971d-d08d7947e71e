"use client";

import { ArrowUpDown } from "lucide-react";
import { Button } from "/@/components/ui/button";

export const columns = [
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    filterable: true,
  },
  { accessorKey: "email", header: "Email", filterable: true },
  { accessorKey: "status", header: "Status", filterable: false },
  {
    id: "actions",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <Button onClick={() => (window.location.href = `users/${user.id}`)}>
          View User
        </Button>
      );
    },
  },
];
