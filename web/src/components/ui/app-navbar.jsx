import { Button } from "./button";
import { SidebarTrigger } from "./sidebar";

export default function Navbar({ className, role }) {
  return (
    <nav
      className={`bg-white dark:bg-gray-700 hover:bg-gray-50 transition duration-400 ease-in-out dark:hover:bg-gray-700 w-full border border-white dark:border-gray-700 hover:border-gray-200 dark:hover:border-gray-900 hover:shadow-xs rounded-md h-10 flex items-center px-4 ${className}`}
    >
      <SidebarTrigger />

      <h3 className="text-xl ml-3 font-semibold">{role}</h3>
      <div className="flex-1"></div>
      <Button variant="outline">Logout</Button>
    </nav>
  );
}
