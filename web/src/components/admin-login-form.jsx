"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Shield, Lock } from "lucide-react";

export function AdminLoginForm({ className, ...props }) {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const login = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // For admin login, we use the public schema admin endpoint
      // This is for platform administrators (superusers)
      const res = await fetch(
        "http://localhost:8000/api/public/admin/login/",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            username: username,
            password: password,
          }),
        }
      );

      const data = await res.json();

      if (res.ok) {
        // Store the authentication token
        localStorage.setItem("adminToken", data.token);
        localStorage.setItem("adminId", data.user_id);
        localStorage.setItem("adminEmail", data.email);

        toast.success("Admin access granted. Redirecting...");
        
        // Redirect to admin dashboard
        router.push("/admin/dashboard/home");
      } else {
        toast.error("Access denied. Invalid credentials.");
      }
    } catch (error) {
      console.error("Admin login error:", error);
      toast.error("Authentication failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0 border border-gray-300 shadow-lg">
        <CardContent className="p-8">
          <form onSubmit={login}>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-gray-600" />
                </div>
                <h1 className="text-xl font-semibold text-gray-800">
                  System Access
                </h1>
                <p className="text-sm text-gray-600">
                  Authorized personnel only
                </p>
              </div>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="username" className="text-sm">Username</Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="Enter username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    className="h-10"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="password" className="text-sm">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    placeholder="Enter password"
                    className="h-10"
                  />
                </div>
              </div>
              <Button 
                type="submit" 
                className="w-full bg-gray-800 hover:bg-gray-900 h-10" 
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Authenticating...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    <span>Access System</span>
                  </div>
                )}
              </Button>
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
                  <Shield className="h-3 w-3" />
                  <span>Secure authentication required</span>
                </div>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
      <div className="text-center text-xs text-gray-500">
        This system is for authorized administrators only. 
        All access attempts are logged and monitored.
      </div>
    </div>
  );
}
