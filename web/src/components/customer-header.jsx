import Image from "next/image";
import Link from "next/link";

const CustomerHeader = () => {
    return (
    <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <Link href="/customer" className="flex items-center gap-2">
              <Image alt="EasyEats" width={50} height={50} src={'/logo1.png'}/>
            </Link>
            <nav className="hidden md:flex items-center gap-6">
              <Link href="/customer/restaurants" className="text-gray-600 hover:text-orange-600">
                Restaurants
              </Link>
              <Link href="/customer/history" className="text-orange-600 font-medium">
                Order History
              </Link>
            </nav>
          </div>
        </div>
      </header>
    )
 }

export default CustomerHeader;