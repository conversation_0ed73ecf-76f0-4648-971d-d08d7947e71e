"use client"

import { useEffect, useState } from "react"
import dynamic from "next/dynamic"

// Dynamically import map components to avoid SSR issues
const MapContainer = dynamic(() => import("react-leaflet").then((mod) => mod.MapContainer), { ssr: false })
const TileLayer = dynamic(() => import("react-leaflet").then((mod) => mod.TileLayer), { ssr: false })
const Marker = dynamic(() => import("react-leaflet").then((mod) => mod.Marker), { ssr: false })
const Popup = dynamic(() => import("react-leaflet").then((mod) => mod.Popup), { ssr: false })

// Custom hook for routing
function useRouting(map, userLocation, restaurantLocation) {
  useEffect(() => {
    if (!map || !userLocation || !restaurantLocation) return

    // Import leaflet-routing-machine dynamically
    import("leaflet-routing-machine").then((L) => {
      const routingControl = L.default.Routing.control({
        waypoints: [
          L.default.latLng(userLocation[0], userLocation[1]),
          L.default.latLng(restaurantLocation[0], restaurantLocation[1]),
        ],
        routeWhileDragging: false,
        addWaypoints: false,
        createMarker: () => null, // We'll handle markers separately
        lineOptions: {
          styles: [{ color: "#ff6b35", weight: 4, opacity: 0.8 }],
        },
      }).addTo(map)

      return () => {
        if (map && routingControl) {
          map.removeControl(routingControl)
        }
      }
    })
  }, [map, userLocation, restaurantLocation])
}

function RouteMapContent({ userLocation, restaurantLocation, restaurantName }) {
  const [map, setMap] = useState(null)

  useRouting(map, userLocation, restaurantLocation)

  return (
    <>
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />

      {userLocation && (
        <Marker position={userLocation}>
          <Popup>
            <div className="text-center">
              <strong>Your Location</strong>
            </div>
          </Popup>
        </Marker>
      )}

      {restaurantLocation && (
        <Marker position={restaurantLocation}>
          <Popup>
            <div className="text-center">
              <strong>{restaurantName}</strong>
              <br />
              <small>Destination</small>
            </div>
          </Popup>
        </Marker>
      )}
    </>
  )
}

export default function RouteMap({ userLocation, restaurantLocation, restaurantName, height = "400px" }) {
  const [isClient, setIsClient] = useState(false)
  const [map, setMap] = useState(null)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return (
      <div className="bg-gray-200 rounded-lg flex items-center justify-center" style={{ height }}>
        <p className="text-gray-500">Loading route map...</p>
      </div>
    )
  }

  if (!userLocation || !restaurantLocation) {
    return (
      <div className="bg-gray-100 rounded-lg flex items-center justify-center" style={{ height }}>
        <p className="text-gray-500">Unable to show route</p>
      </div>
    )
  }

  // Calculate center point between user and restaurant
  const centerLat = (userLocation[0] + restaurantLocation[0]) / 2
  const centerLng = (userLocation[1] + restaurantLocation[1]) / 2

  return (
    <div style={{ height }} className="rounded-lg overflow-hidden border">
      <MapContainer center={[centerLat, centerLng]} zoom={13} style={{ height: "100%", width: "100%" }} ref={setMap}>
        <RouteMapContent
          userLocation={userLocation}
          restaurantLocation={restaurantLocation}
          restaurantName={restaurantName}
        />
      </MapContainer>
    </div>
  )
}
