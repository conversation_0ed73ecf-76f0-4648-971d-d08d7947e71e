"use client"

import { useEffect, useRef, useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, use<PERSON>ap<PERSON><PERSON><PERSON> } from "react-leaflet"
import "leaflet/dist/leaflet.css"
import L from "leaflet"

// Set local marker icons
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: "/icons/marker-icon-2x.png",
  iconUrl: "/icons/marker-icon.png",
  shadowUrl: "/icons/marker-shadow.png",
})

function LocationMarker({ position, setPosition }) {
  useMapEvents({
    click(e) {
      const latlng = [e.latlng.lat, e.latlng.lng]
      setPosition(latlng)
    },
  })

  return position ? <Marker position={position} /> : null
}

export default function MapPicker({ position, setPosition, height = "400px" }) {
  const [isClient, setIsClient] = useState(false)
  const [query, setQuery] = useState("")
  const [results, setResults] = useState([])

  const mapRef = useRef()

  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleSearch = async () => {
    if (!query.trim()) return
    const res = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`,
      {
        headers: {
          "User-Agent": "EasyEats/1.0 (<EMAIL>)",
        },
      }
    )
    const data = await res.json()
    setResults(data)
  }

  const handleSelect = (place) => {
    const lat = parseFloat(place.lat)
    const lon = parseFloat(place.lon)
    const coords = [lat, lon]
    setQuery(place.display_name)
    setResults([])
    setPosition(coords)
    if (mapRef.current) {
      mapRef.current.flyTo(coords, 15)
    }
  }

  const handleUseCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (pos) => {
          const coords = [pos.coords.latitude, pos.coords.longitude]
          setPosition(coords)
          if (mapRef.current) {
            mapRef.current.flyTo(coords, 15)
          }
        },
        (err) => {
          console.error("Location error:", err)
          alert("Unable to get your current location. Please allow location access or click on the map.")
        }
      )
    } else {
      alert("Geolocation is not supported in this browser.")
    }
  }

  const defaultCenter = [-13.9833, 33.7833] // Lilongwe, Malawi

  if (!isClient) {
    return (
      <div className="bg-gray-200 rounded-lg flex items-center justify-center" style={{ height }}>
        <p className="text-gray-500">Loading map...</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search and Current Location */}
      <div className="flex gap-2">
        <input
          className="w-full p-2 border rounded"
          type="text"
          placeholder="Search for a location..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && handleSearch()}
        />
      </div>

      {results.length > 0 && (
        <ul className="border bg-white rounded shadow mt-1 max-h-60 overflow-y-auto z-10 relative">
          {results.map((place, i) => (
            <li
              key={i}
              className="p-2 hover:bg-gray-100 cursor-pointer text-sm"
              onClick={() => handleSelect(place)}
            >
              {place.display_name}
            </li>
          ))}
        </ul>
      )}

      {/* Map */}
      <div className="rounded-lg overflow-hidden border" style={{ height }}>
        <MapContainer
          ref={mapRef}
          center={position || defaultCenter}
          zoom={13}
          style={{ height: "100%", width: "100%" }}
          whenCreated={(mapInstance) => {
            mapRef.current = mapInstance
          }}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          <LocationMarker position={position} setPosition={setPosition} />
        </MapContainer>
      </div>
    </div>
  )
}
