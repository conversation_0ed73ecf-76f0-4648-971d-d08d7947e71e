# Easy Eats Web

Easy Eats Web is a modern, full-featured restaurant discovery and management platform built with the latest web technologies. The project leverages the power of **Next.js 14+ (App Router)** for server-side rendering and routing, **React** for dynamic user interfaces, and **Tailwind CSS** for rapid, responsive, and maintainable styling. The UI is enhanced with custom React components and the Lucide icon set, while state management is handled using React hooks and context for scalability and maintainability.

The application is designed with a modular architecture, separating customer, manager, and admin experiences. It integrates with RESTful APIs (backend not included here) and is optimized for performance, accessibility, and modern deployment platforms like Vercel. Fonts are loaded efficiently using next/font, and the codebase follows best practices for code organization, reusability, and extensibility.

**Key Technologies & Parameters:**

- **Next.js 14+ (App Router):** Modern SSR/SSG, file-based routing, and API integration
- **React (ES6+):** Component-driven UI, hooks, and context for state management
- **Tailwind CSS:** Utility-first, responsive, and maintainable styling
- **Custom UI Components:** Built for reusability and scalability
- **Lucide React Icons:** Modern, consistent iconography
- **RESTful API Integration:** Easily adaptable to any backend
- **Optimized Fonts:** Using next/font for performance
- **Vercel Ready:** Seamless deployment and CI/CD support
- **Best Practices:** Modular structure, clear separation of concerns, and extensible codebase

This project demonstrates expertise in modern web development, UI/UX design, scalable architecture, and developer experience. It is suitable for production-grade SaaS, marketplaces, or any business requiring robust, maintainable, and beautiful web applications.

## Features

### For Customers

- Browse and search for restaurants by name, cuisine, or location
- View featured restaurants and categories
- See restaurant details, ratings, and delivery times
- Personalized recommendations and order history

### For Restaurant Managers

- Dashboard to manage restaurants, menus, and staff
- Add and update restaurant details and dishes
- View performance metrics and recent activity

### For Admins

- Approve or reject restaurant registration requests
- Manage users and restaurants
- Assign custom domains to restaurants

## Technology Stack

- **Framework:** [Next.js 14+](https://nextjs.org/) (App Router)
- **Language:** JavaScript (ES6+), React
- **Styling:** Tailwind CSS, custom CSS modules
- **UI Components:** Custom React components, [Lucide React Icons](https://lucide.dev/)
- **State Management:** React hooks, context
- **APIs:** RESTful API integration (backend not included in this repo)
- **Other:** next/font for optimized font loading, Vercel for deployment

## Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   ```
2. **Run the development server:**
   ```bash
   npm run dev
   # or
   yarn dev
   ```
3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

- `src/app/` – Main application pages (customer, manager, admin, auth, restaurants)
- `src/components/` – Shared and UI components
- `public/` – Static assets (images, icons)
- `styles/` – Global and component styles

## Customization

- Edit `src/app/page.js` for the landing page
- Update components in `src/components/` for UI changes
- API endpoints can be configured in the fetch calls inside page components

## Deployment

Deploy easily to [Vercel](https://vercel.com/) or any platform supporting Next.js.

---

**Note:** This README describes only the web application. For mobile or admin apps, see their respective folders.

Created Users:

Created client: Tahit Restaurant with schema: tahit_restaurant
Created domain: tahit-restaurant.localhost
Created user: <EMAIL>
Password: Pbt0edLOljTindQr
✅ Successfully approved Tahit restaurant!
Login URL: http://tahit-restaurant.localhost:8000
Username: <EMAIL>
Password: Pbt0edLOljTindQr

Created client: Kips with schema: kips
Created domain: kips.localhost
Created user: <EMAIL>
Password: rcVxXfFjXzcVB29k
✅ Successfully approved Kips restaurant!
Login URL: http://kips.localhost:8000
Username: <EMAIL>
Password: rcVxXfFjXzcVB29k
