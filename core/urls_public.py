from django.urls import path, include
from django.contrib import admin
from django.conf import settings
from django.conf.urls.static import static
from tenants.views import tenant_home

urlpatterns = [
    path('', tenant_home, name='tenant-home'),
    path('admin/', admin.site.urls),
    path('api/public/', include('public.api.urls')),
    path('api/tenant/', include('tenants.api.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
