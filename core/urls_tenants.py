from django.urls import path, include
from restaurant.api.views import (
    PaymentViewSet,
    NotificationViewSet,
    RestaurantConfigurationViewSet
)
from tenants.views import tenant_home
from tenants.api.urls import urlpatterns as tenant_auth_urls  # Fix import

urlpatterns = [
    path('', tenant_home, name='tenant-home'),
    path('api/auth/', include(tenant_auth_urls)),  # Use tenant auth URLs
    path('api/payments/', PaymentViewSet.as_view({'get': 'list'})),
    path('api/notifications/', NotificationViewSet.as_view({'get': 'list'})),
    path('api/config/', RestaurantConfigurationViewSet.as_view({'get': 'list', 'put': 'update'})),
]
