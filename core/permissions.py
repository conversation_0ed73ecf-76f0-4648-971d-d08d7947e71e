from rest_framework import permissions
from users.models import UserRole


class IsRestaurantManager(permissions.BasePermission):
    """
    Allows access only to restaurant managers for their own staff.
    """
    def has_permission(self, request, view):
        # Check if user is authenticated and has manager role
        return bool(
            request.user.is_authenticated and
            request.user.role == UserRole.MANAGER and
            request.user.restaurant is not None
        )

    def has_object_permission(self, request, view, obj):
        # Ensure manager only accesses staff from their restaurant
        return obj.restaurant == request.user.restaurant

class IsManagerOrReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user.is_authenticated and request.user.role == UserRole.MANAGER

class IsPublicTenant(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.tenant.schema_name == 'public'

class IsRegisteredCustomer(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_registered_customer

class IsRestaurantManager(permissions.BasePermission):
    """
    Permits only authenticated restaurant managers to access their own staff data
    """
    def has_permission(self, request, view):
        # Verify user is authenticated manager belonging to current tenant
        return bool(
            request.user and
            request.user.is_authenticated and
            request.user.role == UserRole.MANAGER and
            request.user.restaurant == request.tenant
        )

    def has_object_permission(self, request, view, obj):
        # Ensure staff member belongs to manager's restaurant
        return obj.restaurant == request.tenant
